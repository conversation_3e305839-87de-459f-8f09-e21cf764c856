here before you is my crypto aggregator project. it is written using typescript and pure node js framework and ccxt library. currently it works smooth. when user sends a request looking like "ip:port/binance/BTC:USdT:100:BTC", it will start monitoring 'BTC/USDT' symbol orderbook on binance and update it's cached value in 'OrderbookCache' recalculate the price of 100 BTC (buy and sell) with the updated orderbook data and returns this buy and sell price as the request response. 
now here is the thing that is missed and that i'd like to have. i want to also be able to use this API not only in URL fetch mode, but also in socket subscription-based approach. it should support a lot of exchanges and a lot of symbols per connection. having additional symbols volume calculation is not needed for this mode. later i want to split these 2 modes into 2 different apps. the main one will be handing sockets connections and crypto monitoring, and the second one will be handling only http requests with volume calculation. so, can you create a folder 'src/sockets' and put in there 'sockets-server.ts' and 'sockets-client.ts' files and make them communicate with each other, simulating my app's future work? you can take the info about test symbols in ' my-tests-helpers.ts' file. also ideally i want it to be sticking to the best sockets-api patters in crypto industry. meaning it should work similar to an exchange orderbook socket api. you can check for reference how the client communication is implemented for bybit exchange in ccxt library source code in node_modules folder. so ok, here is my project. please don't break it, just  just show me how to properly implement fast and reliable sockets connection for my needs. please analyse all the nuances very carefully and make sure you've included all the important details and don't miss anything.