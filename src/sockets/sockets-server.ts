import { WebSocketServer, WebSocket } from 'ws';
import { logger } from '../utils/pinno-logger';
import { MonitoredRequest } from '../core/monitored-request';
import { updateNotifier } from '../core/orderbook-update-notifier';
import * as ccxt from 'ccxt';

interface SubscriptionRequest {
  exchange: string;
  symbol: string;
  volume?: number;
  volumeSymbol?: string;
}

interface WebSocketMessage {
  op: 'subscribe' | 'unsubscribe' | 'ping';
  args?: SubscriptionRequest[];
  id?: string;
}

interface ClientSubscription {
  client: WebSocket;
  subscriptionKey: string;
  monitoredRequest: MonitoredRequest;
}

export class CryptoWebSocketServer {
  private wss: WebSocketServer;
  private clients = new Map<WebSocket, Set<string>>();
  private subscriptions = new Map<string, ClientSubscription[]>();
  private monitoredRequests = new Map<string, MonitoredRequest>();
  private heartbeatInterval: NodeJS.Timeout;

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port });
    this.setupServer();
    this.startHeartbeat();
    logger.info(`WebSocket server listening on port ${port}`);
  }

  private setupServer() {
    this.wss.on('connection', (ws: WebSocket) => {
      logger.info('New WebSocket connection established');
      this.clients.set(ws, new Set());

      // Send welcome message
      this.sendMessage(ws, {
        event: 'connected',
        data: { message: 'Connected to Crypto Aggregator WebSocket API' }
      });

      ws.on('message', (data: Buffer) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(ws, message);
        } catch (error) {
          logger.error(error, 'Failed to parse WebSocket message');
          this.sendError(ws, 'Invalid JSON message');
        }
      });

      ws.on('close', () => {
        logger.info('WebSocket connection closed');
        this.handleDisconnection(ws);
      });

      ws.on('error', (error) => {
        logger.error(error, 'WebSocket error');
        this.handleDisconnection(ws);
      });

      // Send ping to keep connection alive
      ws.ping();
    });
  }

  private handleMessage(ws: WebSocket, message: WebSocketMessage) {
    switch (message.op) {
      case 'subscribe':
        this.handleSubscribe(ws, message.args || [], message.id);
        break;
      case 'unsubscribe':
        this.handleUnsubscribe(ws, message.args || [], message.id);
        break;
      case 'ping':
        this.sendMessage(ws, { event: 'pong', id: message.id });
        break;
      default:
        this.sendError(ws, `Unknown operation: ${message.op}`, message.id);
    }
  }

  private async handleSubscribe(ws: WebSocket, subscriptions: SubscriptionRequest[], messageId?: string) {
    const results: any[] = [];

    for (const sub of subscriptions) {
      try {
        const subscriptionKey = this.createSubscriptionKey(sub);
        const clientSubscriptions = this.clients.get(ws);
        
        if (clientSubscriptions?.has(subscriptionKey)) {
          results.push({ 
            subscription: sub, 
            status: 'already_subscribed' 
          });
          continue;
        }

        // Create or get existing MonitoredRequest
        let monitoredRequest = this.monitoredRequests.get(subscriptionKey);
        if (!monitoredRequest) {
          const cacheKey = this.createCacheKey(sub);
          monitoredRequest = new MonitoredRequest(cacheKey);
          monitoredRequest.setParsedValues(
            sub.exchange,
            sub.symbol.split('/')[0],
            sub.symbol.split('/')[1],
            sub.volume,
            sub.volumeSymbol
          );

          // Initialize monitoring
          const wasMonitored = monitoredRequest.initialize();
          if (!wasMonitored && monitoredRequest.error) {
            results.push({ 
              subscription: sub, 
              status: 'error', 
              error: monitoredRequest.error.message 
            });
            continue;
          }

          this.monitoredRequests.set(subscriptionKey, monitoredRequest);

          // Subscribe to updates
          updateNotifier.subscribe(sub.exchange, monitoredRequest.symbols as string, (orderbook) => {
            this.handleOrderbookUpdate(subscriptionKey, orderbook);
          });
        }

        // Add client subscription
        const clientSub: ClientSubscription = {
          client: ws,
          subscriptionKey,
          monitoredRequest
        };

        if (!this.subscriptions.has(subscriptionKey)) {
          this.subscriptions.set(subscriptionKey, []);
        }
        this.subscriptions.get(subscriptionKey)!.push(clientSub);
        clientSubscriptions?.add(subscriptionKey);

        results.push({ 
          subscription: sub, 
          status: 'subscribed' 
        });

        // Send current data if available
        if (monitoredRequest.buy !== undefined && monitoredRequest.sell !== undefined) {
          this.sendPriceUpdate(ws, sub, monitoredRequest);
        }

      } catch (error) {
        logger.error(error, `Failed to subscribe to ${JSON.stringify(sub)}`);
        results.push({ 
          subscription: sub, 
          status: 'error', 
          error: error.message 
        });
      }
    }

    this.sendMessage(ws, {
      event: 'subscribe_response',
      data: results,
      id: messageId
    });
  }

  private handleUnsubscribe(ws: WebSocket, subscriptions: SubscriptionRequest[], messageId?: string) {
    const results: any[] = [];

    for (const sub of subscriptions) {
      const subscriptionKey = this.createSubscriptionKey(sub);
      const clientSubscriptions = this.clients.get(ws);

      if (!clientSubscriptions?.has(subscriptionKey)) {
        results.push({ 
          subscription: sub, 
          status: 'not_subscribed' 
        });
        continue;
      }

      // Remove client subscription
      clientSubscriptions.delete(subscriptionKey);
      
      const subs = this.subscriptions.get(subscriptionKey);
      if (subs) {
        const index = subs.findIndex(s => s.client === ws);
        if (index !== -1) {
          subs.splice(index, 1);
        }

        // If no more clients subscribed, cleanup
        if (subs.length === 0) {
          this.subscriptions.delete(subscriptionKey);
          const monitoredRequest = this.monitoredRequests.get(subscriptionKey);
          if (monitoredRequest) {
            monitoredRequest.cleanup();
            this.monitoredRequests.delete(subscriptionKey);
          }
        }
      }

      results.push({ 
        subscription: sub, 
        status: 'unsubscribed' 
      });
    }

    this.sendMessage(ws, {
      event: 'unsubscribe_response',
      data: results,
      id: messageId
    });
  }

  private handleDisconnection(ws: WebSocket) {
    const clientSubscriptions = this.clients.get(ws);
    if (clientSubscriptions) {
      // Unsubscribe from all subscriptions
      const subscriptionsToRemove = Array.from(clientSubscriptions);
      for (const subscriptionKey of subscriptionsToRemove) {
        const subs = this.subscriptions.get(subscriptionKey);
        if (subs) {
          const index = subs.findIndex(s => s.client === ws);
          if (index !== -1) {
            subs.splice(index, 1);
          }

          // Cleanup if no more clients
          if (subs.length === 0) {
            this.subscriptions.delete(subscriptionKey);
            const monitoredRequest = this.monitoredRequests.get(subscriptionKey);
            if (monitoredRequest) {
              monitoredRequest.cleanup();
              this.monitoredRequests.delete(subscriptionKey);
            }
          }
        }
      }
    }
    this.clients.delete(ws);
  }

  private handleOrderbookUpdate(subscriptionKey: string, orderbook: ccxt.OrderBook | Error) {
    const subs = this.subscriptions.get(subscriptionKey);
    if (!subs) return;

    const monitoredRequest = this.monitoredRequests.get(subscriptionKey);
    if (!monitoredRequest) return;

    if (orderbook instanceof Error) {
      // Send error to all subscribed clients
      for (const sub of subs) {
        this.sendMessage(sub.client, {
          event: 'error',
          data: {
            subscription: this.parseSubscriptionKey(subscriptionKey),
            error: orderbook.message
          }
        });
      }
      return;
    }

    // Update the monitored request
    monitoredRequest.updateData(orderbook, Date.now());

    // Send price updates to all subscribed clients
    for (const sub of subs) {
      const subscriptionData = this.parseSubscriptionKey(subscriptionKey);
      this.sendPriceUpdate(sub.client, subscriptionData, monitoredRequest);
    }
  }

  private sendPriceUpdate(ws: WebSocket, subscription: SubscriptionRequest, monitoredRequest: MonitoredRequest) {
    this.sendMessage(ws, {
      event: 'price_update',
      data: {
        exchange: subscription.exchange,
        symbol: subscription.symbol,
        volume: subscription.volume,
        volumeSymbol: subscription.volumeSymbol,
        buy: monitoredRequest.buy,
        sell: monitoredRequest.sell,
        timestamp: monitoredRequest.lastUpdateTime
      }
    });
  }

  private sendMessage(ws: WebSocket, message: any) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private sendError(ws: WebSocket, error: string, messageId?: string) {
    this.sendMessage(ws, {
      event: 'error',
      error,
      id: messageId
    });
  }

  private createSubscriptionKey(sub: SubscriptionRequest): string {
    return `${sub.exchange}:${sub.symbol}:${sub.volume || ''}:${sub.volumeSymbol || ''}`;
  }

  private parseSubscriptionKey(key: string): SubscriptionRequest {
    const [exchange, symbol, volume, volumeSymbol] = key.split(':');
    return {
      exchange,
      symbol,
      volume: volume ? parseFloat(volume) : undefined,
      volumeSymbol: volumeSymbol || undefined
    };
  }

  private createCacheKey(sub: SubscriptionRequest): string {
    const volumePart = sub.volume && sub.volumeSymbol ? `:${sub.volume}:${sub.volumeSymbol}` : '';
    return `/${sub.exchange}/${sub.symbol.replace('/', ':')}${volumePart}`;
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.ping();
        }
      });
    }, 30000); // 30 seconds
  }

  public close() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    this.wss.close();
  }
}

// Export singleton instance
export const cryptoWebSocketServer = new CryptoWebSocketServer(8080);
