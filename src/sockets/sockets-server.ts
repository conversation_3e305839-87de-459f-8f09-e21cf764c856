import { WebSocket, WebSocketServer } from 'ws';
import { logger } from '../utils/pinno-logger';
import { exchanges } from '../core/exchanges-provider';
import { updateNotifier } from '../core/orderbook-update-notifier';
import { orderbookCache } from '../core/orderbook-cache';
import { monitoring } from '../core/orderbook-monitoring-aggregator';
import { ExchangeHelper } from '../utils/exchange-helper';
import * as ccxt from 'ccxt';
import { LINQ } from '../utils/linq';
import {
  WsMessage,
  ErrorMessage,
  Subscribe,
  Subscribed, SubscribeResult,
  Unsubscribe,
  Unsubscribed, UnSubscribedResult,
  ConnectionSuccess, Update
} from './sockets-communication';

export interface ClientConnection {
  ws: WebSocket;
  subscriptions: Set<string>; // Set of topic strings
  lastPing: number;
}

export class CryptoWebSocketServer {
  private wss: WebSocketServer;
  private clients = new Map<WebSocket, ClientConnection>();
  private topicSubscriptions = new Map<string, Set<WebSocket>>(); // topic -> clients
  private activeMonitors = new Map<string, { exchange: string; symbol: string; count: number }>();
  private heartbeatInterval: NodeJS.Timeout;

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port });
    this.setupServer();
    this.startHeartbeatLoop();
    logger.info(`Crypto WebSocket server listening on port ${port}`);
    logger.info('Supported message format: {"op": "subscribe", "args": ["orderbook.BTCUSDT@binance"], "req_id": "1"}');
  }

  private setupServer() {
    this.wss.on('', this.connectNewClient.bind(this));
  }

  private connectNewClient(ws: WebSocket) {
    const clientId = this.generateClientId();
    logger.info(`New WebSocket connection: ${clientId}`);

    const clientConnection: ClientConnection = {
      ws,
      subscriptions: new Set(),
      lastPing: Date.now()
    };
    this.clients.set(ws, clientConnection);
    this.listenHeartbeatResponses(ws, clientConnection);
    this.listenMessages(ws, clientConnection);

    // Send connection success response (Bybit-style)
    this.sendMessage(ws, new ConnectionSuccess());

    ws.on('close', (code, reason) => {
      logger.info(`WebSocket connection closed: ${clientId}, code: ${code}, reason: ${reason}`);
      this.handleDisconnection(ws, clientConnection);
    });

    ws.on('error', (error) => {
      logger.error(error, `WebSocket error for ${clientId}`);
      this.handleDisconnection(ws, clientConnection);
    });
  }


  private listenHeartbeatResponses(ws: WebSocket, client: ClientConnection) {
    const pongHandler =
      () => client.lastPing = Date.now();
    ws.on('pong', pongHandler);

    const closeHandler = () => {
      ws.removeListener('pong', pongHandler);
      ws.removeListener('close', closeHandler);
    };
    ws.on('close', closeHandler);
  }

  private listenMessages(ws: WebSocket, client: ClientConnection) {
    const messageHandler =
      (data: Buffer) => this.handleMessage(ws, client, data);
    ws.on('message', messageHandler);

    const closeHandler = () => {
      ws.removeListener('message', messageHandler);
      ws.removeListener('close', closeHandler);
    };

    ws.on('close', closeHandler);
  }

  private handleMessage(ws: WebSocket, client: ClientConnection, data: Buffer) {
    try {
      const message: WsMessage = JSON.parse(data.toString());
      switch (message.type) {
        case Subscribe.type: {
          this.handleSubscribe(ws, client, message as Subscribe);
          break;
        }
        case Unsubscribe.type: {
          this.handleUnsubscribe(ws, client, message as Unsubscribe);
          break;
        }
        default: {
          this.sendMessage(ws, new ErrorMessage(`Unknown operation: ${message.type}`));
        }
      }
    } catch (error) {
      logger.error(error, 'Failed to parse WebSocket message');
      this.sendMessage(ws, new ErrorMessage('Invalid JSON format'));
    }
  }

  private handleSubscribe(ws: WebSocket, client: ClientConnection, message: Subscribe)   {
    const results: SubscribeResult[] = [];

    for (const topic of message.topics) {
      try {
        const parsedTopic = this.parseTopic(topic);
        if (!parsedTopic) {
          results.push({ topic, status: 'error', error: 'Invalid topic format' });
          continue;
        }

        const { exchange, symbol } = parsedTopic;

        // Check if already subscribed
        if (client.subscriptions.has(topic)) {
          results.push({ topic, status: 'already_subscribed' });
          continue;
        }

        // Validate exchange and symbol
        const exchangeInstance = exchanges.get(exchange);
        if (!exchangeInstance) {
          results.push({ topic, status: 'error', error: `Exchange ${exchange} not supported` });
          continue;
        }

        const symbols = ExchangeHelper.findSupportedSymbols(exchangeInstance,
          symbol.split('/')[0], symbol.split('/')[1]);
        if (!symbols) {
          results.push({ topic, status: 'error', error: `Symbol ${symbol} not supported on ${exchange}` });
          continue;
        }

        // Add to client subscriptions
        client.subscriptions.add(topic);

        // Add to topic subscriptions
        if (!this.topicSubscriptions.has(topic)) {
          this.topicSubscriptions.set(topic, new Set());
        }
        this.topicSubscriptions.get(topic)!.add(ws);

        // Start monitoring if not already active
        const monitorKey = `${exchange}:${symbols}`;
        let monitor = this.activeMonitors.get(monitorKey);
        if (!monitor) {
          monitor = { exchange, symbol: symbols, count: 0 };
          this.activeMonitors.set(monitorKey, monitor);
        }
        monitor.count++;

        results.push({ topic, status: 'subscribed' });

        // Send current orderbook if available
        const cachedOrderbook = orderbookCache.getRecord(exchange, symbols);
        if (cachedOrderbook) {
          this.sendMessage(ws, new Update(topic, cachedOrderbook.orderbook));
        }

      } catch (error) {
        logger.error(error, `Failed to subscribe to topic: ${topic}`);
        results.push({ topic, status: 'error', error: error.message });
      }
    }

    this.sendMessage(ws, new Subscribed(results));
  }

  private handleUnsubscribe(ws: WebSocket, client: ClientConnection, message: Unsubscribe) {
    const results: UnSubscribedResult[] = [];

    for (const topic of message.topics) {
      if (!client.subscriptions.has(topic)) {
        results.push({ topic, status: 'not_subscribed' });
        continue;
      }

      // Remove from client subscriptions
      client.subscriptions.delete(topic);

      // Remove from topic subscriptions
      const topicClients = this.topicSubscriptions.get(topic);
      if (topicClients) {
        topicClients.delete(ws);

        // If no more clients for this topic, cleanup monitoring
        if (topicClients.size === 0) {
          this.topicSubscriptions.delete(topic);

          const parsedTopic = this.parseTopic(topic);
          if (parsedTopic) {
            const monitorKey = `${parsedTopic.exchange}:${parsedTopic.symbol}`;
            const monitor = this.activeMonitors.get(monitorKey);
            if (monitor) {
              monitor.count--;
              if (monitor.count <= 0) {
                this.stopMonitoring(parsedTopic.exchange, parsedTopic.symbol);
                this.activeMonitors.delete(monitorKey);
              }
            }
          }
        }
      }

      results.push({ topic, status: 'unsubscribed' });
    }

    this.sendMessage(ws, new Unsubscribed(results));
  }

  private handleDisconnection(ws: WebSocket, client: ClientConnection) {
    // Unsubscribe from all topics
    const topicsToRemove = LINQ.toArray(client.subscriptions);
    if (topicsToRemove.length > 0) {
      this.handleUnsubscribe(ws, client, new Unsubscribe(topicsToRemove));
    }

    this.clients.delete(ws);
    logger.info(`Client disconnected, ${this.clients.size} clients remaining`);
  }

  private startMonitoring(exchange: string, symbol: string) {
    logger.info(`Starting orderbook monitoring for ${exchange}:${symbol}`);

    // Subscribe to orderbook updates using existing notification system
    updateNotifier.subscribe(exchange, symbol, (orderbook) => {
      this.handleOrderbookUpdate(exchange, symbol, orderbook);
    });

    // Start monitoring using existing monitoring system
    monitoring.startMonitoring(exchange, symbol);
  }

  private stopMonitoring(exchange: string, symbol: string) {
    logger.info(`Stopping orderbook monitoring for ${exchange}:${symbol}`);
    monitoring.stopMonitoring(exchange, symbol);
  }

  private handleOrderbookUpdate(exchange: string, symbol: string, orderbook: ccxt.OrderBook | Error) {
    // Find all topics that match this exchange:symbol
    const matchingTopics = LINQ.where(this.topicSubscriptions.keys(),
      (topic) => {
        const parsed = this.parseTopic(topic);
        return parsed && parsed.exchange === exchange && parsed.symbol === symbol;
      });

    for (const topic of matchingTopics) {
      const clients = this.topicSubscriptions.get(topic);
      if (!clients) continue;

      for (const ws of clients) {
        this.sendMessage(ws, new Update(topic, orderbook));
      }
    }
  }

  private parseTopic(topic: string): { exchange: string; symbol: string } | undefined {
    // Parse topic format: "orderbook.BTCUSDT@binance" (no depth parameter)
    const match = topic.match(/^orderbook\.([A-Z0-9]+)@([a-z]+)$/i);
    if (!match) return undefined;

    const [, symbolId, exchange] = match;
    // Convert BTCUSDT to BTC/USDT format
    const symbol = this.parseSymbolId(symbolId);
    if (!symbol) return undefined;

    return {
      exchange: exchange.toLowerCase(),
      symbol
    };
  }

  private parseSymbolId(symbolId: string): string | undefined {
    // Simple heuristic to split BTCUSDT into BTC/USDT
    // This could be improved with market data lookup
    const stablecoins = ['USDT', 'USDC', 'USD', 'EUR', 'GBP', 'JPY', 'BTC', 'ETH'];

    for (const stable of stablecoins) {
      if (symbolId.endsWith(stable) && symbolId.length > stable.length) {
        const base = symbolId.slice(0, -stable.length);
        return `${base}/${stable}`;
      }
    }

    return undefined;
  }

  private generateClientId(): string {
    return Math.random().toString(36).slice(2, 15);
  }

  private sendMessage(ws: WebSocket, message: unknown) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error(error, 'Failed to send WebSocket message');
      }
    }
  }

  private startHeartbeatLoop() {
    this.heartbeatInterval = setInterval(() => {
      for (const [ws, client] of this.clients) {
        if (ws.readyState !== WebSocket.OPEN) {
          // Clean up dead connections
          this.handleDisconnection(ws, client);
          continue;
        }

        const now = Date.now();
        const timeout = 60_000; // 60 seconds timeout

        // Check if client is still alive
        if (now - client.lastPing > timeout) {
          logger.warn('Client ping timeout, closing connection');
          ws.terminate();
          continue;
        }

        // Send ping
        ws.ping();
      }

      // Log connection stats
      if (this.clients.size > 0) {
        logger.debug(`Active connections: ${this.clients.size}, Active monitors: ${this.activeMonitors.size}`);
      }
    }, 30_000); // 30 seconds
  }

  public getStats() {
    return {
      connections: this.clients.size,
      topics: this.topicSubscriptions.size,
      monitors: this.activeMonitors.size
    };
  }

  public close() {
    logger.info('Shutting down WebSocket server...');

    // Stop all monitoring
    for (const [monitorKey, monitor] of this.activeMonitors) {
      this.stopMonitoring(monitor.exchange, monitor.symbol);
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.wss.close();
    logger.info('WebSocket server closed');
  }
}

// Export singleton instance - but don't auto-start it
export const createCryptoWebSocketServer = (port: number = 8080) => {
  return new CryptoWebSocketServer(port);
};
