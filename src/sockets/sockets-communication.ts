// Simplified WebSocket API - no depth parameter needed
export interface WebSocketMessage {
  op: 'subscribe' | 'unsubscribe' | 'ping';
  args?: string[]; // Array of topic strings like "orderbook.BTCUSDT@binance"
  req_id?: string;
}

export interface ClientConnection {
  ws: WebSocket;
  subscriptions: Set<string>; // Set of topic strings
  lastPing: number;
}

export interface OrderbookUpdate {
  topic: string;
  type: 'orderbook' | 'error';
  ts: number;
  data?: any; // Raw ccxt.OrderBook object
  error?: string; // Error message if type is 'error'
}