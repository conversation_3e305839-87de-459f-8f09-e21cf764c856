import { WebSocket } from 'ws/wrapper.mjs';
import * as ccxt from 'ccxt';

// Simplified WebSocket API - no depth parameter needed
export class WsMessage {
  type: string;
  constructor(type: string) {
    this.type = type;
  }
}

export class ConnectionSuccess extends WsMessage {
  static type = 'cs';
  constructor() {
    super(ConnectionSuccess.type);
  }
}

export class ErrorMessage extends WsMessage {
  static type = 'e';
  message: string;
  constructor(message: string) {
    super(ErrorMessage.type);
    this.message = message;
  }
}

export class Subscribe extends WsMessage {
  static type = 's';
  topics: string[];

  constructor(topics: string[]) {
    super(Subscribe.type);
    this.topics = topics;
  }
}

export type SubscribeResult = {
  topic: string,
  status: 'subscribed' | 'already_subscribed' | 'error',
  error?: string
};

export class Subscribed extends WsMessage {
  static type = 'sd';
  results: SubscribeResult[];

  constructor(results: SubscribeResult[]) {
    super(Subscribed.type);
    this.results = results;
  }
}

export class Unsubscribe extends WsMessage {
  static type = 'us';
  topics: string[];

  constructor(topics: string[]) {
    super(Unsubscribe.type);
    this.topics = topics;
  }
}

export type UnSubscribedResult = {
  topic: string,
  status: 'unsubscribed' | 'not_subscribed' | 'error',
  error?: string
};
export class Unsubscribed extends WsMessage {
  static type = 'usd';
  results: UnSubscribedResult[];

  constructor(results: UnSubscribedResult[]) {
    super(Unsubscribed.type);
    this.results = results;
  }
}

export class Update extends WsMessage {
  static type = 'u';
  topic: string;
  data: ccxt.OrderBook | string;

  constructor(topic: string, data: ccxt.OrderBook | Error) {
    super(Update.type);
    this.topic = topic;
    
    if (data instanceof Error) {
      this.data = data.message;
    } else {
      this.data = data;
    }
  }
}

export interface OrderbookUpdate {
  topic: string;
  type: 'orderbook' | 'error';
  ts: number;
  data?: any; // Raw ccxt.OrderBook object
  error?: string; // Error message if type is 'error'
}