# Crypto WebSocket API

This implementation provides a WebSocket-based orderbook streaming API that follows crypto exchange industry patterns, specifically modeled after Bybit's WebSocket API.

## Architecture

The WebSocket implementation is designed to be the foundation for splitting your crypto aggregator into two separate applications:

1. **Main App** (this WebSocket server): Handles socket connections and crypto monitoring
2. **HTTP App** (existing): Handles HTTP requests with volume calculations

## Features

- ✅ **Exchange-style WebSocket API** following Bybit patterns
- ✅ **Multi-exchange support** (Binance, Bybit, Coinbase, OKX, etc.)
- ✅ **Multiple symbols per connection**
- ✅ **Real-time orderbook streaming**
- ✅ **Automatic reconnection** with exponential backoff
- ✅ **Connection management** with heartbeat/ping-pong
- ✅ **Error handling** and graceful degradation
- ✅ **Integration** with existing monitoring system
- ✅ **Subscription management** (subscribe/unsubscribe)
- ✅ **Configurable orderbook depth** (1, 50, 200, 500 levels)

## Message Format

### Subscription Request (Client → Server)
```json
{
  "op": "subscribe",
  "args": [
    "orderbook.50.BTCUSDT@binance",
    "orderbook.50.ETHUSDT@bybit"
  ],
  "req_id": "1"
}
```

### Subscription Response (Server → Client)
```json
{
  "success": true,
  "ret_msg": "subscribe",
  "req_id": "1",
  "op": "subscribe",
  "data": [
    {"topic": "orderbook.50.BTCUSDT@binance", "status": "subscribed"},
    {"topic": "orderbook.50.ETHUSDT@bybit", "status": "subscribed"}
  ]
}
```

### Orderbook Update (Server → Client)
```json
{
  "topic": "orderbook.50.BTCUSDT@binance",
  "type": "snapshot",
  "ts": 1672304484978,
  "data": {
    "s": "BTCUSDT",
    "b": [["16493.50", "0.006"], ["16493.00", "0.100"]],
    "a": [["16611.00", "0.029"], ["16612.00", "0.213"]],
    "u": 7961638724,
    "seq": 7961638724
  }
}
```

## Topic Format

Topics follow the pattern: `orderbook.{depth}.{symbol}@{exchange}`

Examples:
- `orderbook.50.BTCUSDT@binance` - BTC/USDT on Binance with 50 levels
- `orderbook.1.ETHUSDT@bybit` - ETH/USDT on Bybit with best bid/ask only

## Usage

### Starting the Server
```typescript
import { createCryptoWebSocketServer } from './sockets-server';
const server = createCryptoWebSocketServer(8080);
```

### Using the Client
```typescript
import { CryptoWebSocketClient } from './sockets-client';

const client = new CryptoWebSocketClient('ws://localhost:8080');
await client.connect();

const topics = [
  client.createTopic('binance', 'BTC/USDT', 50),
  client.createTopic('bybit', 'ETH/USDT', 50)
];

await client.subscribe(topics, 'my-request-1');
```

### Running the Demo
```bash
# Basic demo
npx ts-node src/sockets/demo.ts

# Advanced demo
npx ts-node src/sockets/demo.ts advanced
```

## Integration

Integrates with your existing monitoring infrastructure:
- **OrderbookCache**: Shares cached orderbook data
- **UpdateNotifier**: Receives real-time updates
- **Monitoring System**: Uses existing exchange monitoring
- **ExchangesProvider**: Leverages existing exchange instances

## Performance

- **Shared Monitoring**: Multiple clients share orderbook monitors
- **Efficient Broadcasting**: Updates sent only to subscribed clients
- **Memory Management**: Automatic cleanup of unused resources
- **Connection Pooling**: Reuses existing exchange connections
