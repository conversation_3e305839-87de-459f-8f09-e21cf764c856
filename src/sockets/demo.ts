import { logger } from '../utils/pinno-logger';
import { createCryptoWebSocketServer } from './sockets-server';
import { CryptoWebSocketClient, createTestTopics } from './sockets-client';
import { config } from '../my-tests/my-tests-helpers';

/**
 * Demo script showing how to use the WebSocket server and client
 * This simulates the future architecture where the main app handles WebSocket connections
 * and crypto monitoring, separate from HTTP requests with volume calculations.
 */

async function runDemo() {
  logger.info('🚀 Starting Crypto WebSocket Demo');
  logger.info('This demonstrates orderbook streaming following crypto exchange patterns');

  // Start the WebSocket server
  const server = createCryptoWebSocketServer(8080);
  logger.info('📡 WebSocket server started on port 8080');

  // Wait a moment for server to initialize
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Create multiple clients to simulate real usage
  const clients: CryptoWebSocketClient[] = [];
  
  try {
    // Client 1: Subscribe to major pairs on Binance
    const client1 = new CryptoWebSocketClient();
    await client1.connect();
    clients.push(client1);

    const binanceTopics = [
      client1.createTopic('binance', 'BTC/USDT'),
      client1.createTopic('binance', 'ETH/USDT'),
      client1.createTopic('binance', 'BNB/USDT')
    ];
    await client1.subscribe(binanceTopics, 'client1-binance');

    // Client 2: Subscribe to same pairs on different exchanges
    const client2 = new CryptoWebSocketClient();
    await client2.connect();
    clients.push(client2);

    const multiExchangeTopics = [
      client2.createTopic('bybit', 'BTC/USDT'),
      client2.createTopic('okx', 'ETH/USDT'),
      client2.createTopic('coinbase', 'BTC/USD')
    ];
    await client2.subscribe(multiExchangeTopics, 'client2-multi');

    // Client 3: Subscribe to many symbols (stress test)
    const client3 = new CryptoWebSocketClient();
    await client3.connect();
    clients.push(client3);

    const stressTestTopics = createTestTopics(
      config.exchanges.slice(0, 2), // First 2 exchanges
      config.symbols.slice(0, 3),   // First 3 symbols
      50
    );
    await client3.subscribe(stressTestTopics, 'client3-stress');

    // Let all clients stream data
    logger.info('📊 All clients connected and streaming...');
    logger.info(`📈 Server stats: ${JSON.stringify(server.getStats())}`);
    
    // Stream for 45 seconds
    await new Promise(resolve => setTimeout(resolve, 45000));

    // Demonstrate dynamic subscription management
    logger.info('🔄 Testing dynamic subscription changes...');
    
    // Client 1: Add more subscriptions
    const additionalTopics = [
      client1.createTopic('binance', 'SOL/USDT'),
      client1.createTopic('binance', 'DOGE/USDT')
    ];
    await client1.subscribe(additionalTopics, 'client1-additional');

    // Client 2: Remove some subscriptions
    await client2.unsubscribe([multiExchangeTopics[0]], 'client2-remove');

    // Stream for another 30 seconds
    await new Promise(resolve => setTimeout(resolve, 30000));

    logger.info('📊 Final server stats:', server.getStats());

  } catch (error) {
    logger.error(error, '❌ Demo error');
  } finally {
    // Cleanup
    logger.info('🧹 Cleaning up...');
    
    // Disconnect all clients
    for (const client of clients) {
      client.disconnect();
    }

    // Close server
    server.close();
    
    logger.info('✅ Demo completed');
    process.exit(0);
  }
}

/**
 * Advanced demo showing integration patterns
 */
async function runAdvancedDemo() {
  logger.info('🚀 Starting Advanced WebSocket Demo');
  
  const server = createCryptoWebSocketServer(8080);
  await new Promise(resolve => setTimeout(resolve, 1000));

  const client = new CryptoWebSocketClient();
  await client.connect();

  try {
    // Demonstrate raw orderbook streaming
    const rawTopics = [
      client.createTopic('binance', 'BTC/USDT'),
      client.createTopic('bybit', 'BTC/USDT'),
      client.createTopic('okx', 'BTC/USDT')
    ];

    await client.subscribe(rawTopics, 'raw-demo');

    logger.info('📊 Streaming raw orderbook data for BTC/USDT across exchanges...');
    await new Promise(resolve => setTimeout(resolve, 20000));

    // Test error handling
    const invalidTopics = [
      'invalid.topic.format',
      client.createTopic('nonexistent', 'BTC/USDT'),
      client.createTopic('binance', 'INVALID/PAIR')
    ];

    logger.info('🧪 Testing error handling with invalid topics...');
    await client.subscribe(invalidTopics, 'error-test');
    
    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    logger.error(error, 'Advanced demo error');
  } finally {
    client.disconnect();
    server.close();
    logger.info('✅ Advanced demo completed');
    process.exit(0);
  }
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'advanced':
    runAdvancedDemo().catch(console.error);
    break;
  case 'basic':
  default:
    runDemo().catch(console.error);
    break;
}

// Export for use in other modules
export { runDemo, runAdvancedDemo };
