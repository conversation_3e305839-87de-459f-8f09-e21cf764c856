import WebSocket from 'ws';
import { logger } from '../utils/pinno-logger';
import { config } from '../my-tests/my-tests-helpers';
import { WsMessage, OrderbookUpdate } from './sockets-communication';
import { LINQ } from '../utils/linq';

export class CryptoWebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = undefined;
  private subscriptions = new Set<string>(); // Set of topic strings
  private requestId = 1;

  constructor(url: string = 'ws://localhost:8080') {
    this.url = url;
  }

  private generateRequestId(): string {
    return (this.requestId++).toString();
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.on('open', () => {
          logger.info('Connected to Crypto WebSocket server');
          this.reconnectAttempts = 0;
          this.startPing();
          resolve();
        });

        this.ws.on('message', (data: Buffer) => {
          try {
            const message = JSON.parse(data.toString());
            this.handleMessage(message);
          } catch (error) {
            logger.error(error, 'Failed to parse message from server');
          }
        });

        this.ws.on('close', (code, reason) => {
          logger.warn(`WebSocket connection closed: ${code} ${reason}`);
          this.stopPing();
          this.handleReconnect();
        });

        this.ws.on('error', (error) => {
          logger.error(error, 'WebSocket error');
          reject(error);
        });

        this.ws.on('pong', () => {
          // Connection is alive
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  private handleMessage(message: any) {
    // Handle different message types based on Bybit-style responses
    if (message.topic) {
      // Orderbook update
      this.handleOrderbookUpdate(message as OrderbookUpdate);
    } else if (message.op) {
      // Operation response
      this.handleOperationResponse(message);
    } else if (message.type === 'AUTH_RESP') {
      // Connection response
      logger.info(`Connected to server: ${message.ret_msg}, conn_id: ${message.conn_id}`);
    } else {
      logger.debug('Unknown message format:', message);
    }
  }

  private handleOrderbookUpdate(update: OrderbookUpdate) {
    if (update.type === 'error') {
      logger.error(`Orderbook error for ${update.topic}: ${update.error}`);
      return;
    }

    // Raw ccxt.OrderBook object received
    const orderbook = update.data;
    const exchange = this.extractExchangeFromTopic(update.topic);
    const symbol = orderbook.symbol || 'Unknown';

    const bestBid = orderbook.bids && orderbook.bids.length > 0 ? orderbook.bids[0][0] : 0;
    const bestAsk = orderbook.asks && orderbook.asks.length > 0 ? orderbook.asks[0][0] : 0;
    const spread = bestAsk - bestBid;
    const spreadPercent = bestBid > 0 ? (spread / bestBid * 100) : 0;

    logger.info(
      `📊 ${exchange.toUpperCase()} ${symbol}: ` +
      `Bid: $${bestBid.toFixed(2)} | Ask: $${bestAsk.toFixed(2)} | ` +
      `Spread: $${spread.toFixed(2)} (${spreadPercent.toFixed(3)}%) | ` +
      `Depth: ${orderbook.bids?.length || 0}/${orderbook.asks?.length || 0} | ` +
      `Time: ${new Date(update.ts).toISOString()}`
    );
  }

  private handleOperationResponse(message: any) {
    const { op, success, ret_msg, req_id, data } = message;

    switch (op) {
      case 'subscribe': {
        logger.info(`Subscribe response (${req_id}): ${success ? 'SUCCESS' : 'FAILED'} - ${ret_msg}`);
        if (data && Array.isArray(data)) {
          for (const result of data) {
            let status = '';
            if (result.status === 'subscribed') {
              status = '✅';
            } else if (result.status === 'already_subscribed') {
              status = '⚠️';
            } else {
              status = '❌';
            }
            logger.info(`  ${status} ${result.topic}: ${result.status}`);
            if (result.error) {
              logger.error(`    Error: ${result.error}`);
            }
          }
        }

        break;
      }
      case 'unsubscribe': {
        logger.info(`Unsubscribe response (${req_id}): ${success ? 'SUCCESS' : 'FAILED'} - ${ret_msg}`);
        if (data && Array.isArray(data)) {
          for (const result of data) {
            logger.info(`  🔄 ${result.topic}: ${result.status}`);
          }
        }

        break;
      }
      case 'ping': {
        // Pong received

        break;
      }
      case 'error': {
        logger.error(`Server error (${req_id}): ${ret_msg}`);

        break;
      }
      // No default
    }
  }

  public async subscribe(topics: string[], reqId?: string): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    const message: WsMessage = {
      op: 'subscribe',
      args: topics,
      req_id: reqId || this.generateRequestId()
    };

    // Track subscriptions
    for (let i = 0; i < topics.length; i++){
      this.subscriptions.add(topics[i]);
    }

    this.ws.send(JSON.stringify(message));
    logger.info(`📡 Subscribing to ${topics.length} topics: ${topics.join(', ')}`);
  }

  public async unsubscribe(topics: string[], reqId?: string): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    const message: WsMessage = {
      op: 'unsubscribe',
      args: topics,
      req_id: reqId || this.generateRequestId()
    };

    // Remove from tracked subscriptions
    for (let i = 0; i < topics.length; i++){
      this.subscriptions.delete(topics[i]);
    }

    this.ws.send(JSON.stringify(message));
    logger.info(`📡 Unsubscribing from ${topics.length} topics: ${topics.join(', ')}`);
  }

  // Helper method to create topic strings (no depth parameter)
  public createTopic(exchange: string, symbol: string): string {
    const symbolId = symbol.replace('/', '');
    return `orderbook.${symbolId}@${exchange.toLowerCase()}`;
  }

  public ping(reqId?: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const message: WsMessage = {
      op: 'ping',
      req_id: reqId || this.generateRequestId()
    };

    this.ws.send(JSON.stringify(message));
  }

  private extractExchangeFromTopic(topic: string): string {
    const match = topic.match(/@([a-z]+)$/i);
    return match ? match[1] : 'unknown';
  }

  private startPing() {
    this.pingInterval = setInterval(() => {
      this.ping();
    }, 30_000); // 30 seconds
  }

  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(async () => {
      try {
        await this.connect();
        // Resubscribe to previous subscriptions
        if (this.subscriptions.size > 0) {
          const topics = LINQ.toArray(this.subscriptions);
          await this.subscribe(topics, 'reconnect');
        }
      } catch (error) {
        logger.error(error, 'Reconnection failed');
        this.handleReconnect();
      }
    }, delay);
  }

  public getSubscriptions(): string[] {
    return LINQ.toArray(this.subscriptions);
  }

  public disconnect() {
    this.stopPing();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    logger.info('Disconnected from WebSocket server');
  }
}

// Demo function to test the WebSocket client
export async function runWebSocketDemo() {
  const client = new CryptoWebSocketClient();

  try {
    // Connect to server
    logger.info('🚀 Starting WebSocket demo...');
    await client.connect();

    // Wait a bit for connection to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create test topics using symbols from config
    const testTopics = [
      client.createTopic('binance', 'BTC/USDT'),
      client.createTopic('binance', 'ETH/USDT'),
      client.createTopic('bybit', 'BTC/USDT'),
      client.createTopic('coinbase', 'BTC/USD'),
      client.createTopic('okx', 'ETH/USDT')
    ];

    // Subscribe to test topics
    await client.subscribe(testTopics, 'demo-1');

    // Let it run for 30 seconds
    logger.info('📊 Streaming orderbook data for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    // Unsubscribe from some topics
    const topicsToRemove = testTopics.slice(0, 2);
    await client.unsubscribe(topicsToRemove, 'demo-2');

    // Run for another 15 seconds
    logger.info('📊 Continuing with remaining subscriptions for 15 seconds...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    // Disconnect
    client.disconnect();
    logger.info('✅ Demo completed successfully');

  } catch (error) {
    logger.error(error, '❌ Demo failed');
    client.disconnect();
  }
}

// Helper function to create topics from config symbols
export function createTestTopics(exchanges: string[], symbols: string[]): string[] {
  const topics: string[] = [];
  const client = new CryptoWebSocketClient();

  for (const exchange of exchanges.slice(0, 3)) { // Limit to first 3 exchanges
    for (const symbol of symbols.slice(0, 5)) { // Limit to first 5 symbols
      for (const stable of config.stables.slice(0, 1)) { // Use only USDT
        const symbolPair = `${symbol}/${stable}`;
        topics.push(client.createTopic(exchange, symbolPair));
      }
    }
  }

  return topics;
}

// Run demo if this file is executed directly
if (require.main === module) {
  runWebSocketDemo().catch(console.error);
}
