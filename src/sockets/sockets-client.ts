import WebSocket from 'ws';
import { logger } from '../utils/pinno-logger';
import { config } from '../my-tests/my-tests-helpers';

interface SubscriptionRequest {
  exchange: string;
  symbol: string;
  volume?: number;
  volumeSymbol?: string;
}

interface WebSocketMessage {
  op: 'subscribe' | 'unsubscribe' | 'ping';
  args?: SubscriptionRequest[];
  id?: string;
}

interface PriceUpdate {
  exchange: string;
  symbol: string;
  volume?: number;
  volumeSymbol?: string;
  buy: number;
  sell: number;
  timestamp: number;
}

export class CryptoWebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  private subscriptions = new Set<string>();

  constructor(url: string = 'ws://localhost:8080') {
    this.url = url;
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.on('open', () => {
          logger.info('Connected to WebSocket server');
          this.reconnectAttempts = 0;
          this.startPing();
          resolve();
        });

        this.ws.on('message', (data: Buffer) => {
          try {
            const message = JSON.parse(data.toString());
            this.handleMessage(message);
          } catch (error) {
            logger.error(error, 'Failed to parse message from server');
          }
        });

        this.ws.on('close', (code, reason) => {
          logger.warn(`WebSocket connection closed: ${code} ${reason}`);
          this.stopPing();
          this.handleReconnect();
        });

        this.ws.on('error', (error) => {
          logger.error(error, 'WebSocket error');
          reject(error);
        });

        this.ws.on('pong', () => {
          // Connection is alive
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  private handleMessage(message: any) {
    switch (message.event) {
      case 'connected':
        logger.info('Server connection confirmed:', message.data.message);
        break;
      case 'price_update':
        this.handlePriceUpdate(message.data);
        break;
      case 'subscribe_response':
        this.handleSubscribeResponse(message.data, message.id);
        break;
      case 'unsubscribe_response':
        this.handleUnsubscribeResponse(message.data, message.id);
        break;
      case 'error':
        logger.error('Server error:', message.error || message.data);
        break;
      case 'pong':
        // Pong received
        break;
      default:
        logger.debug('Unknown message type:', message.event);
    }
  }

  private handlePriceUpdate(data: PriceUpdate) {
    const volumeInfo = data.volume && data.volumeSymbol ? ` (${data.volume} ${data.volumeSymbol})` : '';
    logger.info(
      `💰 ${data.exchange.toUpperCase()} ${data.symbol}${volumeInfo}: ` +
      `Buy: $${data.buy?.toFixed(2)} | Sell: $${data.sell?.toFixed(2)} | ` +
      `Time: ${new Date(data.timestamp).toISOString()}`
    );
  }

  private handleSubscribeResponse(results: any[], messageId?: string) {
    logger.info(`Subscribe response (${messageId}):`);
    results.forEach(result => {
      const sub = result.subscription;
      const volumeInfo = sub.volume && sub.volumeSymbol ? ` (${sub.volume} ${sub.volumeSymbol})` : '';
      logger.info(`  ${sub.exchange}/${sub.symbol}${volumeInfo}: ${result.status}`);
      if (result.error) {
        logger.error(`    Error: ${result.error}`);
      }
    });
  }

  private handleUnsubscribeResponse(results: any[], messageId?: string) {
    logger.info(`Unsubscribe response (${messageId}):`);
    results.forEach(result => {
      const sub = result.subscription;
      const volumeInfo = sub.volume && sub.volumeSymbol ? ` (${sub.volume} ${sub.volumeSymbol})` : '';
      logger.info(`  ${sub.exchange}/${sub.symbol}${volumeInfo}: ${result.status}`);
    });
  }

  public async subscribe(subscriptions: SubscriptionRequest[], id?: string): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    const message: WebSocketMessage = {
      op: 'subscribe',
      args: subscriptions,
      id
    };

    // Track subscriptions
    subscriptions.forEach(sub => {
      const key = this.createSubscriptionKey(sub);
      this.subscriptions.add(key);
    });

    this.ws.send(JSON.stringify(message));
    logger.info(`Sent subscribe request for ${subscriptions.length} subscriptions`);
  }

  public async unsubscribe(subscriptions: SubscriptionRequest[], id?: string): Promise<void> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected');
    }

    const message: WebSocketMessage = {
      op: 'unsubscribe',
      args: subscriptions,
      id
    };

    // Remove from tracked subscriptions
    subscriptions.forEach(sub => {
      const key = this.createSubscriptionKey(sub);
      this.subscriptions.delete(key);
    });

    this.ws.send(JSON.stringify(message));
    logger.info(`Sent unsubscribe request for ${subscriptions.length} subscriptions`);
  }

  public ping(id?: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    const message: WebSocketMessage = {
      op: 'ping',
      id
    };

    this.ws.send(JSON.stringify(message));
  }

  private startPing() {
    this.pingInterval = setInterval(() => {
      this.ping();
    }, 30000); // 30 seconds
  }

  private stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private async handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(async () => {
      try {
        await this.connect();
        // Resubscribe to previous subscriptions
        if (this.subscriptions.size > 0) {
          const subs = Array.from(this.subscriptions).map(key => this.parseSubscriptionKey(key));
          await this.subscribe(subs, 'reconnect');
        }
      } catch (error) {
        logger.error(error, 'Reconnection failed');
        this.handleReconnect();
      }
    }, delay);
  }

  private createSubscriptionKey(sub: SubscriptionRequest): string {
    return `${sub.exchange}:${sub.symbol}:${sub.volume || ''}:${sub.volumeSymbol || ''}`;
  }

  private parseSubscriptionKey(key: string): SubscriptionRequest {
    const [exchange, symbol, volume, volumeSymbol] = key.split(':');
    return {
      exchange,
      symbol,
      volume: volume ? parseFloat(volume) : undefined,
      volumeSymbol: volumeSymbol || undefined
    };
  }

  public disconnect() {
    this.stopPing();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Demo function to test the WebSocket client
export async function runWebSocketDemo() {
  const client = new CryptoWebSocketClient();

  try {
    // Connect to server
    await client.connect();

    // Wait a bit for connection to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Subscribe to some test symbols
    const testSubscriptions: SubscriptionRequest[] = [
      { exchange: 'binance', symbol: 'BTC/USDT' },
      { exchange: 'binance', symbol: 'ETH/USDT', volume: 10, volumeSymbol: 'ETH' },
      { exchange: 'bybit', symbol: 'BTC/USDT', volume: 1, volumeSymbol: 'BTC' },
      { exchange: 'coinbase', symbol: 'BTC/USD' }
    ];

    await client.subscribe(testSubscriptions, 'demo-1');

    // Let it run for 30 seconds
    logger.info('Running demo for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    // Unsubscribe from some
    await client.unsubscribe([testSubscriptions[0], testSubscriptions[1]], 'demo-2');

    // Run for another 15 seconds
    await new Promise(resolve => setTimeout(resolve, 15000));

    // Disconnect
    client.disconnect();
    logger.info('Demo completed');

  } catch (error) {
    logger.error(error, 'Demo failed');
    client.disconnect();
  }
}

// Run demo if this file is executed directly
if (require.main === module) {
  runWebSocketDemo().catch(console.error);
}
