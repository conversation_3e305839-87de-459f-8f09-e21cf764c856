import { WebSocket, WebSocketServer } from 'ws/wrapper.mjs';
import { ClientConnection, WebSocketMessage } from '../sockets/sockets-communication';
import { logger } from '../utils/pinno-logger';
import { LINQ } from '../utils/linq';

export class CryptoWebSocketServer {
  private wss: WebSocketServer;
  private clients = new Map<WebSocket, ClientConnection>();
  private topicSubscriptions = new Map<string, Set<WebSocket>>(); // topic -> clients
  private activeMonitors = new Map<string, { exchange: string; symbol: string; count: number }>();
  private heartbeatInterval: NodeJS.Timeout;

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ port });
    this.setupServer();
    this.startHeartbeat();
    logger.info(`Crypto WebSocket server listening on port ${port}`);
    logger.info('Supported message format: {"op": "subscribe", "args": ["orderbook.BTCUSDT@binance"], "req_id": "1"}');
  }

  private setupServer() {
    this.wss.on('connection', (ws: WebSocket) => {
      const clientId = this.generateClientId();
      logger.info(`New WebSocket connection: ${clientId}`);

      const clientConnection: ClientConnection = {
        ws,
        subscriptions: new Set(),
        lastPing: Date.now()
      };
      this.clients.set(ws, clientConnection);

      // Send connection success response (Bybit-style)
      this.sendMessage(ws, {
        success: true,
        ret_msg: 'Connected successfully',
        conn_id: clientId,
        type: 'AUTH_RESP'
      });

      ws.on('message', (data: Buffer) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(ws, message);
        } catch (error) {
          logger.error(error, 'Failed to parse WebSocket message');
          this.sendError(ws, 'Invalid JSON format', undefined);
        }
      });

      ws.on('close', (code, reason) => {
        logger.info(`WebSocket connection closed: ${clientId}, code: ${code}, reason: ${reason}`);
        this.handleDisconnection(ws);
      });

      ws.on('error', (error) => {
        logger.error(error, `WebSocket error for ${clientId}`);
        this.handleDisconnection(ws);
      });

      ws.on('pong', () => {
        const client = this.clients.get(ws);
        if (client) {
          client.lastPing = Date.now();
        }
      });
    });
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const timeout = 60_000; // 60 seconds timeout

      for (const [ws, client] of this.clients.entries()) {
        if (ws.readyState === WebSocket.OPEN) {
          // Check if client is still alive
          if (now - client.lastPing > timeout) {
            logger.warn('Client ping timeout, closing connection');
            ws.terminate();
            continue;
          }

          // Send ping
          ws.ping();
        } else {
          // Clean up dead connections
          this.handleDisconnection(ws);
        }
      }

      // Log connection stats
      if (this.clients.size > 0) {
        logger.debug(`Active connections: ${this.clients.size}, Active monitors: ${this.activeMonitors.size}`);
      }
    }, 30_000); // 30 seconds
  }

  private handleDisconnection(ws: WebSocket) {
    const client = this.clients.get(ws);
    if (!client) return;

    // Unsubscribe from all topics
    const topicsToRemove = LINQ.toArray(client.subscriptions);
    if (topicsToRemove.length > 0) {
      this.handleUnsubscribe(ws, topicsToRemove);
    }

    this.clients.delete(ws);
    logger.info(`Client disconnected, ${this.clients.size} clients remaining`);
  }
  
  public close() {
    logger.info('Shutting down WebSocket server...');

    // Stop all monitoring
    for (const [monitorKey, monitor] of this.activeMonitors) {
      this.stopMonitoring(monitor.exchange, monitor.symbol);
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    this.wss.close();
    logger.info('WebSocket server closed');
  }
}