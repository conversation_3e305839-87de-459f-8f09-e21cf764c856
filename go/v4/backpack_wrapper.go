package ccxt

type Backpack struct {
	*backpack
	Core          *backpack
	exchangeTyped *ExchangeTyped
}

func NewBackpack(userConfig map[string]interface{}) *Backpack {
	p := &backpack{}
	p.Init(userConfig)
	return &Backpack{
		backpack:      p,
		Core:          p,
		exchangeTyped: NewExchangeTyped(&p.Exchange),
	}
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

/**
 * @method
 * @name backpack#fetchCurrencies
 * @description fetches all available currencies on an exchange
 * @see https://docs.backpack.exchange/#tag/Assets
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an associative dictionary of currencies
 */
func (this *Backpack) FetchCurrencies(params ...interface{}) (Currencies, error) {
	res := <-this.Core.FetchCurrencies(params...)
	if IsError(res) {
		return Currencies{}, CreateReturnError(res)
	}
	return NewCurrencies(res), nil
}

/**
 * @method
 * @name backpack#fetchMarkets
 * @description retrieves data on all markets for bitbank
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_markets
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *Backpack) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	res := <-this.Core.FetchMarkets(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarketInterfaceArray(res), nil
}

/**
 * @method
 * @name backpack#fetchTickers
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_tickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Backpack) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {

	opts := FetchTickersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTickers(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}

/**
 * @method
 * @name backpack#fetchTicker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_ticker
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Backpack) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {

	opts := FetchTickerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTicker(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}

/**
 * @method
 * @name backpack#fetchOrderBook
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_depth
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return (default 100, max 200)
 * @param {object} [params] extra parameters specific to the bitteam api endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://github.com/ccxt/ccxt/wiki/Manual#order-book-structure} indexed by market symbols
 */
func (this *Backpack) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {

	opts := FetchOrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}

/**
 * @method
 * @name backpack#fetchOHLCV
 * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_klines
 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
 * @param {string} timeframe the length of time each candle represents
 * @param {int} [since] timestamp in seconds of the earliest candle to fetch
 * @param {int} [limit] the maximum amount of candles to fetch (default 100)
 * @param {object} [params] extra parameters specific to the bitteam api endpoint
 * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
 */
func (this *Backpack) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {

	opts := FetchOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}

/**
 * @method
 * @name backpack#fetchFundingRate
 * @description fetch the current funding rate
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_mark_prices
 * @param {string} symbol unified market symbol
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [funding rate structure]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
 */
func (this *Backpack) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {

	opts := FetchFundingRateOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchFundingRate(symbol, params)
	if IsError(res) {
		return FundingRate{}, CreateReturnError(res)
	}
	return NewFundingRate(res), nil
}

/**
 * @method
 * @name backpack#fetchOpenInterest
 * @description Retrieves the open interest of a derivative trading pair
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_open_interest
 * @param {string} symbol Unified CCXT market symbol
 * @param {object} [params] exchange specific parameters
 * @returns {object} an open interest structure{@link https://docs.ccxt.com/#/?id=interest-history-structure}
 */
func (this *Backpack) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {

	opts := FetchOpenInterestOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenInterest(symbol, params)
	if IsError(res) {
		return OpenInterest{}, CreateReturnError(res)
	}
	return NewOpenInterest(res), nil
}

/**
 * @method
 * @name backpack#fetchFundingRateHistory
 * @description fetches historical funding rate prices
 * @see https://docs.backpack.exchange/#tag/Markets/operation/get_funding_interval_rates
 * @param {string} symbol unified symbol of the market to fetch the funding rate history for
 * @param {int} [since] timestamp in ms of the earliest funding rate to fetch
 * @param {int} [limit] the maximum amount of funding rate structures
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure}
 */
func (this *Backpack) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {

	opts := FetchFundingRateHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchFundingRateHistory(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewFundingRateHistoryArray(res), nil
}

/**
 * @method
 * @name backpack#fetchTrades
 * @description get the list of most recent trades for a particular symbol
 * @see https://docs.backpack.exchange/#tag/Trades/operation/get_recent_trades
 * @see https://docs.backpack.exchange/#tag/Trades/operation/get_historical_trades
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.offset] the number of trades to skip, default is 0
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *Backpack) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {

	opts := FetchTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name backpack#fetchMyTrades
 * @description fetch all trades made by the user
 * @see https://docs.backpack.exchange/#tag/History/operation/get_fills
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades structures to retrieve (default 100, max 1000)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch trades for
 * @param {string} [params.fillType] 'User' (default) 'BookLiquidation' or 'Adl' or 'Backstop' or 'Liquidation' or 'AllLiquidation' or 'CollateralConversion' or 'CollateralConversionAndSpotLiquidation'
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *Backpack) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {

	opts := FetchMyTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchMyTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name backpack#fetchStatus
 * @description the latest known information on the availability of the exchange API
 * @see https://docs.backpack.exchange/#tag/System/operation/get_status
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
 */
func (this *Backpack) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	res := <-this.Core.FetchStatus(params...)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}

/**
 * @method
 * @name backpack#fetchTime
 * @description fetches the current integer timestamp in milliseconds from the exchange server
 * @see https://developer-pro.bitmart.com/en/spot/#get-system-time
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {int} the current integer timestamp in milliseconds from the exchange server
 */
func (this *Backpack) FetchTime(params ...interface{}) (int64, error) {
	res := <-this.Core.FetchTime(params...)
	if IsError(res) {
		return -1, CreateReturnError(res)
	}
	return (res).(int64), nil
}

/**
 * @method
 * @name backpack#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @see https://docs.backpack.exchange/#tag/Capital/operation/get_balances
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *Backpack) FetchBalance(params ...interface{}) (Balances, error) {
	res := <-this.Core.FetchBalance(params...)
	if IsError(res) {
		return Balances{}, CreateReturnError(res)
	}
	return NewBalances(res), nil
}

/**
 * @method
 * @name backpack#fetchDeposits
 * @description fetch all deposits made to an account
 * @see https://docs.backpack.exchange/#tag/Capital/operation/get_deposits
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch deposits for
 * @param {int} [limit] the maximum number of deposits structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch entries for
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Backpack) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {

	opts := FetchDepositsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDeposits(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name backpack#fetchWithdrawals
 * @description fetch all withdrawals made from an account
 * @see https://docs.backpack.exchange/#tag/Capital/operation/get_withdrawals
 * @param {string} code unified currency code of the currency transferred
 * @param {int} [since] the earliest time in ms to fetch transfers for (default 24 hours ago)
 * @param {int} [limit] the maximum number of transfer structures to retrieve (default 50, max 200)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch transfers for (default time now)
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Backpack) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {

	opts := FetchWithdrawalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchWithdrawals(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name backpack#withdraw
 * @description make a withdrawal
 * @see https://docs.backpack.exchange/#tag/Capital/operation/request_withdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.network] the network to withdraw on (mandatory)
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Backpack) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {

	opts := WithdrawOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var tag interface{} = nil
	if opts.Tag != nil {
		tag = *opts.Tag
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.Withdraw(code, amount, address, tag, params)
	if IsError(res) {
		return Transaction{}, CreateReturnError(res)
	}
	return NewTransaction(res), nil
}

/**
 * @method
 * @name backpack#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://docs.backpack.exchange/#tag/Capital/operation/get_deposit_address
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.networkCode] the network to fetch the deposit address (mandatory)
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *Backpack) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {

	opts := FetchDepositAddressOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDepositAddress(code, params)
	if IsError(res) {
		return DepositAddress{}, CreateReturnError(res)
	}
	return NewDepositAddress(res), nil
}

/**
 * @method
 * @name backpack#createOrder
 * @description create a trade order
 * @see https://docs.backpack.exchange/#tag/Order/operation/execute_order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fullfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {float} [params.cost] *market orders only* the cost of the order in units of the quote currency (could be used instead of amount)
 * @param {int} [params.clientOrderId] a unique id for the order
 * @param {boolean} [params.postOnly] true to place a post only order
 * @param {string} [params.timeInForce] 'GTC', 'IOC', 'FOK' or 'PO'
 * @param {bool} [params.reduceOnly] *contract only* Indicates if this order is to reduce the size of a position
 * @param {string} [params.selfTradePrevention] 'RejectTaker', 'RejectMaker' or 'RejectBoth'
 * @param {bool} [params.autoLend] *spot margin only* if true then the order can lend
 * @param {bool} [params.autoLendRedeem] *spot margin only* if true then the order can redeem a lend if required
 * @param {bool} [params.autoBorrow] *spot margin only* if true then the order can borrow
 * @param {bool} [params.autoBorrowRepay] *spot margin only* if true then the order can repay a borrow
 * @param {float} [params.triggerPrice] the price that a trigger order is triggered at
 * @param {object} [params.takeProfit] *swap markets only - takeProfit object in params* containing the triggerPrice at which the attached take profit order will be triggered
 * @param {float} [params.takeProfit.triggerPrice] take profit trigger price
 * @param {float} [params.takeProfit.price] take profit order price (if not provided the order will be a market order)
 * @param {object} [params.stopLoss] *swap markets only - stopLoss object in params* containing the triggerPrice at which the attached stop loss order will be triggered
 * @param {float} [params.stopLoss.triggerPrice] stop loss trigger price
 * @param {float} [params.stopLoss.price] stop loss order price (if not provided the order will be a market order)
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {

	opts := CreateOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name backpack#createOrders
 * @description create a list of trade orders
 * @see https://docs.backpack.exchange/#tag/Order/operation/execute_order_batch
 * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {

	opts := CreateOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrders(ConvertOrderRequestListToArray(orders), params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name backpack#fetchOpenOrders
 * @description fetch all unfilled currently open orders
 * @see https://docs.backpack.exchange/#tag/Order/operation/get_open_orders
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {

	opts := FetchOpenOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name backpack#fetchOpenOrder
 * @description fetch an open order by it's id
 * @see https://docs.backpack.exchange/#tag/Order/operation/get_order
 * @param {string} id order id
 * @param {string} symbol not used by hollaex fetchOpenOrder ()
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) FetchOpenOrder(id string, options ...FetchOpenOrderOptions) (Order, error) {

	opts := FetchOpenOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name backpack#cancelOrder
 * @description cancels an open order
 * @see https://docs.backpack.exchange/#tag/Order/operation/cancel_order
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {

	opts := CancelOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name backpack#cancelAllOrders
 * @description cancel all open orders
 * @see https://docs.backpack.exchange/#tag/Order/operation/cancel_open_orders
 * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Backpack) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {

	opts := CancelAllOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelAllOrders(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name backpack#fetchOrders
 * @description fetches information on multiple orders made by the user
 * @see https://docs.backpack.exchange/#tag/History/operation/get_order_history
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of  orde structures to retrieve (default 100, max 1000)
 * @param {object} [params] extra parameters specific to the bitteam api endpoint
 * @returns {Order[]} a list of [order structures]{@link https://github.com/ccxt/ccxt/wiki/Manual#order-structure}
 */
func (this *Backpack) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {

	opts := FetchOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name backpack#fetchPositions
 * @description fetch all open positions
 * @see https://docs.backpack.exchange/#tag/Futures/operation/get_positions
 * @param {string[]|undefined} symbols list of unified market symbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
 */
func (this *Backpack) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {

	opts := FetchPositionsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchPositions(symbols, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}

/**
 * @method
 * @name backpack#fetchFundingHistory
 * @description fetches the history of funding payments
 * @see https://docs.backpack.exchange/#tag/History/operation/get_funding_payments
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch (default 24 hours ago)
 * @param {int} [limit] the maximum amount of trades to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest trade to fetch (default now)
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *Backpack) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {

	opts := FetchFundingHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchFundingHistory(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewFundingHistoryArray(res), nil
}

// missing typed methods from base
// nolint
func (this *Backpack) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	return this.exchangeTyped.LoadMarkets(params...)
}
func (this *Backpack) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.CancelAllOrdersAfter(timeout, options...)
}
func (this *Backpack) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {
	return this.exchangeTyped.CancelOrdersForSymbols(orders, options...)
}
func (this *Backpack) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.CreateConvertTrade(id, fromCode, toCode, options...)
}
func (this *Backpack) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.CreateDepositAddress(code, options...)
}
func (this *Backpack) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitBuyOrder(symbol, amount, price, options...)
}
func (this *Backpack) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitOrder(symbol, side, amount, price, options...)
}
func (this *Backpack) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitSellOrder(symbol, amount, price, options...)
}
func (this *Backpack) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrder(symbol, amount, options...)
}
func (this *Backpack) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrderWithCost(symbol, cost, options...)
}
func (this *Backpack) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrder(symbol, side, amount, options...)
}
func (this *Backpack) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrderWithCost(symbol, side, cost, options...)
}
func (this *Backpack) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrder(symbol, amount, options...)
}
func (this *Backpack) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrderWithCost(symbol, cost, options...)
}
func (this *Backpack) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {
	return this.exchangeTyped.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreatePostOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateReduceOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, options...)
}
func (this *Backpack) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLossOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopMarketOrder(symbol, side, amount, triggerPrice, options...)
}
func (this *Backpack) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTakeProfitOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingAmountOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingPercentOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTriggerOrder(symbol, typeVar, side, amount, options...)
}
func (this *Backpack) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitBuyOrder(id, symbol, amount, options...)
}
func (this *Backpack) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitOrder(id, symbol, side, amount, options...)
}
func (this *Backpack) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitSellOrder(id, symbol, amount, options...)
}
func (this *Backpack) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {
	return this.exchangeTyped.EditOrder(id, symbol, typeVar, side, options...)
}
func (this *Backpack) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.EditOrders(orders, options...)
}
func (this *Backpack) FetchAccounts(params ...interface{}) ([]Account, error) {
	return this.exchangeTyped.FetchAccounts(params...)
}
func (this *Backpack) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {
	return this.exchangeTyped.FetchAllGreeks(options...)
}
func (this *Backpack) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {
	return this.exchangeTyped.FetchBidsAsks(options...)
}
func (this *Backpack) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {
	return this.exchangeTyped.FetchBorrowInterest(options...)
}
func (this *Backpack) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchBorrowRate(code, amount, options...)
}
func (this *Backpack) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchCanceledAndClosedOrders(options...)
}
func (this *Backpack) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchClosedOrders(options...)
}
func (this *Backpack) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchConvertCurrencies(params...)
}
func (this *Backpack) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertQuote(fromCode, toCode, options...)
}
func (this *Backpack) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertTrade(id, options...)
}
func (this *Backpack) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {
	return this.exchangeTyped.FetchConvertTradeHistory(options...)
}
func (this *Backpack) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {
	return this.exchangeTyped.FetchCrossBorrowRate(code, options...)
}
func (this *Backpack) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	return this.exchangeTyped.FetchCrossBorrowRates(params...)
}
func (this *Backpack) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddresses(options...)
}
func (this *Backpack) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddressesByNetwork(code, options...)
}
func (this *Backpack) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDepositsWithdrawals(options...)
}
func (this *Backpack) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFee(code, options...)
}
func (this *Backpack) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFees(options...)
}
func (this *Backpack) FetchFreeBalance(params ...interface{}) (Balance, error) {
	return this.exchangeTyped.FetchFreeBalance(params...)
}
func (this *Backpack) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingInterval(symbol, options...)
}
func (this *Backpack) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingIntervals(options...)
}
func (this *Backpack) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingRates(options...)
}
func (this *Backpack) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {
	return this.exchangeTyped.FetchGreeks(symbol, options...)
}
func (this *Backpack) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchIndexOHLCV(symbol, options...)
}
func (this *Backpack) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRate(symbol, options...)
}
func (this *Backpack) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRates(params...)
}
func (this *Backpack) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {
	return this.exchangeTyped.FetchLastPrices(options...)
}
func (this *Backpack) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {
	return this.exchangeTyped.FetchLedger(options...)
}
func (this *Backpack) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {
	return this.exchangeTyped.FetchLedgerEntry(id, options...)
}
func (this *Backpack) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {
	return this.exchangeTyped.FetchLeverage(symbol, options...)
}
func (this *Backpack) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {
	return this.exchangeTyped.FetchLeverages(options...)
}
func (this *Backpack) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {
	return this.exchangeTyped.FetchLeverageTiers(options...)
}
func (this *Backpack) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchLiquidations(symbol, options...)
}
func (this *Backpack) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatio(symbol, options...)
}
func (this *Backpack) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatioHistory(options...)
}
func (this *Backpack) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {
	return this.exchangeTyped.FetchMarginAdjustmentHistory(options...)
}
func (this *Backpack) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {
	return this.exchangeTyped.FetchMarginMode(symbol, options...)
}
func (this *Backpack) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {
	return this.exchangeTyped.FetchMarginModes(options...)
}
func (this *Backpack) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {
	return this.exchangeTyped.FetchMarketLeverageTiers(symbol, options...)
}
func (this *Backpack) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchMarkOHLCV(symbol, options...)
}
func (this *Backpack) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {
	return this.exchangeTyped.FetchMarkPrice(symbol, options...)
}
func (this *Backpack) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {
	return this.exchangeTyped.FetchMarkPrices(options...)
}
func (this *Backpack) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchMyLiquidations(options...)
}
func (this *Backpack) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterestHistory(symbol, options...)
}
func (this *Backpack) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {
	return this.exchangeTyped.FetchOpenInterests(options...)
}
func (this *Backpack) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {
	return this.exchangeTyped.FetchOption(symbol, options...)
}
func (this *Backpack) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {
	return this.exchangeTyped.FetchOptionChain(code, options...)
}
func (this *Backpack) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {
	return this.exchangeTyped.FetchOrder(id, options...)
}
func (this *Backpack) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {
	return this.exchangeTyped.FetchOrderBooks(options...)
}
func (this *Backpack) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {
	return this.exchangeTyped.FetchOrderStatus(id, options...)
}
func (this *Backpack) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchOrderTrades(id, options...)
}
func (this *Backpack) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPaymentMethods(params...)
}
func (this *Backpack) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {
	return this.exchangeTyped.FetchPosition(symbol, options...)
}
func (this *Backpack) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionHistory(symbol, options...)
}
func (this *Backpack) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPositionMode(options...)
}
func (this *Backpack) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsForSymbol(symbol, options...)
}
func (this *Backpack) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsHistory(options...)
}
func (this *Backpack) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsRisk(options...)
}
func (this *Backpack) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchPremiumIndexOHLCV(symbol, options...)
}
func (this *Backpack) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {
	return this.exchangeTyped.FetchTradingFee(symbol, options...)
}
func (this *Backpack) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	return this.exchangeTyped.FetchTradingFees(params...)
}
func (this *Backpack) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTradingLimits(options...)
}
func (this *Backpack) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFee(code, options...)
}
func (this *Backpack) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFees(options...)
}
func (this *Backpack) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchTransactions(options...)
}
func (this *Backpack) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.FetchTransfer(id, options...)
}
func (this *Backpack) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {
	return this.exchangeTyped.FetchTransfers(options...)
}
func (this *Backpack) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {
	return this.exchangeTyped.SetMargin(symbol, amount, options...)
}
func (this *Backpack) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetMarginMode(marginMode, options...)
}
func (this *Backpack) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetPositionMode(hedged, options...)
}
func (this *Backpack) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.Transfer(code, amount, fromAccount, toAccount, options...)
}
