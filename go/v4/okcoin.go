package ccxt

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

type okcoin struct {
	Exchange
}

func NewOkcoinCore() *okcoin {
	p := &okcoin{}
	setDefaults(p)
	return p
}

func (this *okcoin) Describe() interface{} {
	return this.DeepExtend(this.Exchange.Describe(), map[string]interface{}{
		"id":        "okcoin",
		"name":      "OKCoin",
		"countries": []interface{}{"CN", "US"},
		"version":   "v5",
		"rateLimit": 20,
		"pro":       true,
		"has": map[string]interface{}{
			"CORS":                           nil,
			"spot":                           true,
			"margin":                         false,
			"swap":                           false,
			"future":                         true,
			"option":                         nil,
			"cancelOrder":                    true,
			"createMarketBuyOrderWithCost":   true,
			"createMarketOrderWithCost":      false,
			"createMarketSellOrderWithCost":  false,
			"createOrder":                    true,
			"createPostOnlyOrder":            true,
			"createReduceOnlyOrder":          true,
			"createStopLimitOrder":           true,
			"createStopMarketOrder":          true,
			"createStopOrder":                true,
			"createTriggerOrder":             true,
			"fetchBalance":                   true,
			"fetchBorrowInterest":            false,
			"fetchBorrowRate":                false,
			"fetchBorrowRateHistories":       false,
			"fetchBorrowRateHistory":         false,
			"fetchBorrowRates":               false,
			"fetchBorrowRatesPerSymbol":      false,
			"fetchClosedOrders":              true,
			"fetchCurrencies":                true,
			"fetchDepositAddress":            true,
			"fetchDepositAddresses":          false,
			"fetchDepositAddressesByNetwork": false,
			"fetchDeposits":                  true,
			"fetchFundingHistory":            false,
			"fetchFundingRate":               false,
			"fetchFundingRateHistory":        false,
			"fetchFundingRates":              false,
			"fetchLedger":                    true,
			"fetchMarkets":                   true,
			"fetchMyTrades":                  true,
			"fetchOHLCV":                     true,
			"fetchOpenOrders":                true,
			"fetchOrder":                     true,
			"fetchOrderBook":                 true,
			"fetchOrders":                    nil,
			"fetchOrderTrades":               true,
			"fetchPosition":                  false,
			"fetchPositions":                 false,
			"fetchTicker":                    true,
			"fetchTickers":                   true,
			"fetchTime":                      true,
			"fetchTrades":                    true,
			"fetchTransactions":              nil,
			"fetchWithdrawals":               true,
			"reduceMargin":                   false,
			"repayCrossMargin":               false,
			"repayIsolatedMargin":            false,
			"setMargin":                      false,
			"transfer":                       true,
			"withdraw":                       true,
		},
		"timeframes": map[string]interface{}{
			"1m":  "1m",
			"3m":  "3m",
			"5m":  "5m",
			"15m": "15m",
			"30m": "30m",
			"1h":  "1H",
			"2h":  "2H",
			"4h":  "4H",
			"6h":  "6H",
			"12h": "12H",
			"1d":  "1D",
			"1w":  "1W",
			"1M":  "1M",
			"3M":  "3M",
		},
		"hostname": "okcoin.com",
		"urls": map[string]interface{}{
			"logo": "https://user-images.githubusercontent.com/********/********-102fbf00-c50e-11ea-90a9-462eebba5829.jpg",
			"api": map[string]interface{}{
				"rest": "https://www.{hostname}",
			},
			"www":      "https://www.okcoin.com",
			"doc":      "https://www.okcoin.com/docs/en/",
			"fees":     "https://www.okcoin.com/coin-fees",
			"referral": "https://www.okcoin.com/account/register?flag=activity&channelId=*********",
			"test": map[string]interface{}{
				"rest": "https://testnet.okex.com",
			},
		},
		"api": map[string]interface{}{
			"public": map[string]interface{}{
				"get": map[string]interface{}{
					"market/tickers":            1,
					"market/ticker":             1,
					"market/books":              Divide(1, 2),
					"market/candles":            Divide(1, 2),
					"market/history-candles":    Divide(1, 2),
					"market/trades":             Divide(1, 5),
					"market/history-trades":     2,
					"market/platform-24-volume": 10,
					"market/open-oracle":        50,
					"market/exchange-rate":      20,
					"public/instruments":        1,
					"public/time":               2,
				},
			},
			"private": map[string]interface{}{
				"get": map[string]interface{}{
					"trade/order":                   Divide(1, 3),
					"trade/orders-pending":          Divide(1, 3),
					"trade/orders-history":          Divide(1, 2),
					"trade/orders-history-archive":  Divide(1, 2),
					"trade/fills":                   Divide(1, 3),
					"trade/fills-history":           2.2,
					"trade/fills-archive":           2,
					"trade/order-algo":              1,
					"trade/orders-algo-pending":     1,
					"trade/orders-algo-history":     1,
					"otc/rfq/trade":                 4,
					"otc/rfq/history":               4,
					"account/balance":               2,
					"account/bills":                 Divide(5, 3),
					"account/bills-archive":         Divide(5, 3),
					"account/config":                4,
					"account/max-size":              4,
					"account/max-avail-size":        4,
					"account/trade-fee":             4,
					"account/max-withdrawal":        4,
					"asset/currencies":              Divide(5, 3),
					"asset/balances":                Divide(5, 3),
					"asset/asset-valuation":         10,
					"asset/transfer-state":          10,
					"asset/bills":                   Divide(5, 3),
					"asset/deposit-lightning":       5,
					"asset/deposit-address":         Divide(5, 3),
					"asset/deposit-history":         Divide(5, 3),
					"asset/withdrawal-history":      Divide(5, 3),
					"asset/deposit-withdraw-status": 20,
					"fiat/deposit-history":          Divide(5, 3),
					"fiat-withdraw-history":         Divide(5, 3),
					"fiat-channel":                  Divide(5, 3),
					"users/subaccount/list":         10,
					"users/subaccount/apiKey":       10,
					"account/subaccount/balances":   10,
					"asset/subaccount/balances":     10,
					"asset/subaccount/bills":        10,
				},
				"post": map[string]interface{}{
					"trade/order":                Divide(1, 3),
					"trade/batch-orders":         Divide(1, 15),
					"trade/cancel-order":         Divide(1, 3),
					"trade/cancel-batch-orders":  Divide(1, 15),
					"trade/amend-order":          Divide(1, 3),
					"trade/amend-batch-orders":   Divide(1, 150),
					"trade/order-algo":           1,
					"trade/cancel-algos":         1,
					"trade/cancel-advance-algos": 1,
					"otc/rfq/quote":              4,
					"otc/rfq/trade":              4,
					"asset/transfer":             4,
					"asset/withdrawal":           4,
					"asset/withdrawal-lightning": 4,
					"asset/withdrawal-cancel":    4,
					"fiat/deposit":               Divide(5, 3),
					"fiat/cancel-deposit":        Divide(5, 3),
					"fiat/withdrawal":            Divide(5, 3),
					"fiat/cancel-withdrawal":     Divide(5, 3),
					"asset/subaccount/transfer":  10,
				},
			},
		},
		"features": map[string]interface{}{
			"spot": map[string]interface{}{
				"sandbox": false,
				"createOrder": map[string]interface{}{
					"marginMode":       true,
					"triggerPrice":     true,
					"triggerDirection": true,
					"triggerPriceType": map[string]interface{}{
						"last":  true,
						"mark":  false,
						"index": false,
					},
					"stopLossPrice":   true,
					"takeProfitPrice": true,
					"attachedStopLossTakeProfit": map[string]interface{}{
						"triggerPriceType": map[string]interface{}{
							"last":  true,
							"mark":  false,
							"index": false,
						},
						"price": true,
					},
					"timeInForce": map[string]interface{}{
						"IOC": true,
						"FOK": true,
						"PO":  true,
						"GTD": false,
					},
					"hedged":                 false,
					"trailing":               true,
					"leverage":               false,
					"marketBuyByCost":        true,
					"marketBuyRequiresPrice": true,
					"selfTradePrevention":    false,
					"iceberg":                true,
				},
				"createOrders": nil,
				"fetchMyTrades": map[string]interface{}{
					"marginMode":     false,
					"limit":          100,
					"daysBack":       90,
					"untilDays":      90,
					"symbolRequired": false,
				},
				"fetchOrder": map[string]interface{}{
					"marginMode":     false,
					"trigger":        true,
					"trailing":       true,
					"symbolRequired": true,
				},
				"fetchOpenOrders": map[string]interface{}{
					"marginMode":     false,
					"limit":          100,
					"trigger":        true,
					"trailing":       true,
					"symbolRequired": false,
				},
				"fetchOrders": nil,
				"fetchClosedOrders": map[string]interface{}{
					"marginMode":       false,
					"limit":            100,
					"daysBack":         90,
					"daysBackCanceled": Divide(1, 12),
					"untilDays":        90,
					"trigger":          true,
					"trailing":         true,
					"symbolRequired":   false,
				},
				"fetchOHLCV": map[string]interface{}{
					"limit": 100,
				},
			},
			"swap": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
			"future": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
		},
		"fees": map[string]interface{}{
			"trading": map[string]interface{}{
				"taker": 0.002,
				"maker": 0.001,
			},
			"spot": map[string]interface{}{
				"taker": 0.0015,
				"maker": 0.001,
			},
		},
		"requiredCredentials": map[string]interface{}{
			"apiKey":   true,
			"secret":   true,
			"password": true,
		},
		"exceptions": map[string]interface{}{
			"exact": map[string]interface{}{
				"1":     ExchangeError,
				"2":     ExchangeError,
				"50000": BadRequest,
				"50001": OnMaintenance,
				"50002": BadRequest,
				"50004": RequestTimeout,
				"50005": ExchangeNotAvailable,
				"50006": BadRequest,
				"50007": AccountSuspended,
				"50008": AuthenticationError,
				"50009": AccountSuspended,
				"50010": ExchangeError,
				"50011": RateLimitExceeded,
				"50012": ExchangeError,
				"50013": ExchangeNotAvailable,
				"50014": BadRequest,
				"50015": ExchangeError,
				"50016": ExchangeError,
				"50017": ExchangeError,
				"50018": ExchangeError,
				"50019": ExchangeError,
				"50020": ExchangeError,
				"50021": ExchangeError,
				"50022": ExchangeError,
				"50023": ExchangeError,
				"50024": BadRequest,
				"50025": ExchangeError,
				"50026": ExchangeNotAvailable,
				"50027": PermissionDenied,
				"50028": ExchangeError,
				"50029": ExchangeError,
				"50030": PermissionDenied,
				"50032": AccountSuspended,
				"50033": AccountSuspended,
				"50035": BadRequest,
				"50036": BadRequest,
				"50037": BadRequest,
				"50038": ExchangeError,
				"50039": ExchangeError,
				"50041": ExchangeError,
				"50044": BadRequest,
				"50100": ExchangeError,
				"50101": AuthenticationError,
				"50102": InvalidNonce,
				"50103": AuthenticationError,
				"50104": AuthenticationError,
				"50105": AuthenticationError,
				"50106": AuthenticationError,
				"50107": AuthenticationError,
				"50108": ExchangeError,
				"50109": ExchangeError,
				"50110": PermissionDenied,
				"50111": AuthenticationError,
				"50112": AuthenticationError,
				"50113": AuthenticationError,
				"50114": AuthenticationError,
				"50115": BadRequest,
				"51000": BadRequest,
				"51001": BadSymbol,
				"51002": BadSymbol,
				"51003": BadRequest,
				"51004": InvalidOrder,
				"51005": InvalidOrder,
				"51006": InvalidOrder,
				"51007": InvalidOrder,
				"51008": InsufficientFunds,
				"51009": AccountSuspended,
				"51010": AccountNotEnabled,
				"51011": InvalidOrder,
				"51012": BadSymbol,
				"51014": BadSymbol,
				"51015": BadSymbol,
				"51016": InvalidOrder,
				"51017": ExchangeError,
				"51018": ExchangeError,
				"51019": ExchangeError,
				"51020": InvalidOrder,
				"51023": ExchangeError,
				"51024": AccountSuspended,
				"51025": ExchangeError,
				"51026": BadSymbol,
				"51030": InvalidOrder,
				"51031": InvalidOrder,
				"51032": InvalidOrder,
				"51033": InvalidOrder,
				"51037": InvalidOrder,
				"51038": InvalidOrder,
				"51044": InvalidOrder,
				"51046": InvalidOrder,
				"51047": InvalidOrder,
				"51048": InvalidOrder,
				"51049": InvalidOrder,
				"51050": InvalidOrder,
				"51051": InvalidOrder,
				"51052": InvalidOrder,
				"51053": InvalidOrder,
				"51054": BadRequest,
				"51056": InvalidOrder,
				"51058": InvalidOrder,
				"51059": InvalidOrder,
				"51100": InvalidOrder,
				"51102": InvalidOrder,
				"51103": InvalidOrder,
				"51108": InvalidOrder,
				"51109": InvalidOrder,
				"51110": InvalidOrder,
				"51111": BadRequest,
				"51112": InvalidOrder,
				"51113": RateLimitExceeded,
				"51115": InvalidOrder,
				"51116": InvalidOrder,
				"51117": InvalidOrder,
				"51118": InvalidOrder,
				"51119": InsufficientFunds,
				"51120": InvalidOrder,
				"51121": InvalidOrder,
				"51122": InvalidOrder,
				"51124": InvalidOrder,
				"51125": InvalidOrder,
				"51126": InvalidOrder,
				"51127": InsufficientFunds,
				"51128": InvalidOrder,
				"51129": InvalidOrder,
				"51130": BadSymbol,
				"51131": InsufficientFunds,
				"51132": InvalidOrder,
				"51133": InvalidOrder,
				"51134": InvalidOrder,
				"51135": InvalidOrder,
				"51136": InvalidOrder,
				"51137": InvalidOrder,
				"51138": InvalidOrder,
				"51139": InvalidOrder,
				"51156": BadRequest,
				"51159": BadRequest,
				"51162": InvalidOrder,
				"51163": InvalidOrder,
				"51166": InvalidOrder,
				"51174": InvalidOrder,
				"51201": InvalidOrder,
				"51202": InvalidOrder,
				"51203": InvalidOrder,
				"51204": InvalidOrder,
				"51205": InvalidOrder,
				"51250": InvalidOrder,
				"51251": InvalidOrder,
				"51252": InvalidOrder,
				"51253": InvalidOrder,
				"51254": InvalidOrder,
				"51255": InvalidOrder,
				"51256": InvalidOrder,
				"51257": InvalidOrder,
				"51258": InvalidOrder,
				"51259": InvalidOrder,
				"51260": InvalidOrder,
				"51261": InvalidOrder,
				"51262": InvalidOrder,
				"51263": InvalidOrder,
				"51264": InvalidOrder,
				"51265": InvalidOrder,
				"51267": InvalidOrder,
				"51268": InvalidOrder,
				"51269": InvalidOrder,
				"51270": InvalidOrder,
				"51271": InvalidOrder,
				"51272": InvalidOrder,
				"51273": InvalidOrder,
				"51274": InvalidOrder,
				"51275": InvalidOrder,
				"51276": InvalidOrder,
				"51277": InvalidOrder,
				"51278": InvalidOrder,
				"51279": InvalidOrder,
				"51280": InvalidOrder,
				"51321": InvalidOrder,
				"51322": InvalidOrder,
				"51323": BadRequest,
				"51324": BadRequest,
				"51325": InvalidOrder,
				"51327": InvalidOrder,
				"51328": InvalidOrder,
				"51329": InvalidOrder,
				"51330": InvalidOrder,
				"51400": OrderNotFound,
				"51401": OrderNotFound,
				"51402": OrderNotFound,
				"51403": InvalidOrder,
				"51404": InvalidOrder,
				"51405": ExchangeError,
				"51406": ExchangeError,
				"51407": BadRequest,
				"51408": ExchangeError,
				"51409": ExchangeError,
				"51410": CancelPending,
				"51500": ExchangeError,
				"51501": ExchangeError,
				"51502": InsufficientFunds,
				"51503": ExchangeError,
				"51506": ExchangeError,
				"51508": ExchangeError,
				"51509": ExchangeError,
				"51510": ExchangeError,
				"51511": ExchangeError,
				"51600": ExchangeError,
				"51601": ExchangeError,
				"51602": ExchangeError,
				"51603": OrderNotFound,
				"51732": AuthenticationError,
				"51733": AuthenticationError,
				"51734": AuthenticationError,
				"51735": ExchangeError,
				"51736": InsufficientFunds,
				"52000": ExchangeError,
				"54000": ExchangeError,
				"54001": ExchangeError,
				"58000": ExchangeError,
				"58001": AuthenticationError,
				"58002": PermissionDenied,
				"58003": ExchangeError,
				"58004": AccountSuspended,
				"58005": ExchangeError,
				"58006": ExchangeError,
				"58007": ExchangeError,
				"58100": ExchangeError,
				"58101": AccountSuspended,
				"58102": RateLimitExceeded,
				"58103": ExchangeError,
				"58104": ExchangeError,
				"58105": ExchangeError,
				"58106": ExchangeError,
				"58107": ExchangeError,
				"58108": ExchangeError,
				"58109": ExchangeError,
				"58110": ExchangeError,
				"58111": ExchangeError,
				"58112": ExchangeError,
				"58114": ExchangeError,
				"58115": ExchangeError,
				"58116": ExchangeError,
				"58117": ExchangeError,
				"58125": BadRequest,
				"58126": BadRequest,
				"58127": BadRequest,
				"58128": BadRequest,
				"58200": ExchangeError,
				"58201": ExchangeError,
				"58202": ExchangeError,
				"58203": InvalidAddress,
				"58204": AccountSuspended,
				"58205": ExchangeError,
				"58206": ExchangeError,
				"58207": InvalidAddress,
				"58208": ExchangeError,
				"58209": ExchangeError,
				"58210": ExchangeError,
				"58211": ExchangeError,
				"58212": ExchangeError,
				"58213": AuthenticationError,
				"58221": BadRequest,
				"58222": BadRequest,
				"58224": BadRequest,
				"58227": BadRequest,
				"58228": BadRequest,
				"58229": InsufficientFunds,
				"58300": ExchangeError,
				"58350": InsufficientFunds,
				"59000": ExchangeError,
				"59001": ExchangeError,
				"59100": ExchangeError,
				"59101": ExchangeError,
				"59102": ExchangeError,
				"59103": InsufficientFunds,
				"59104": ExchangeError,
				"59105": ExchangeError,
				"59106": ExchangeError,
				"59107": ExchangeError,
				"59108": InsufficientFunds,
				"59109": ExchangeError,
				"59128": InvalidOrder,
				"59200": InsufficientFunds,
				"59201": InsufficientFunds,
				"59216": BadRequest,
				"59300": ExchangeError,
				"59301": ExchangeError,
				"59313": ExchangeError,
				"59401": ExchangeError,
				"59500": ExchangeError,
				"59501": ExchangeError,
				"59502": ExchangeError,
				"59503": ExchangeError,
				"59504": ExchangeError,
				"59505": ExchangeError,
				"59506": ExchangeError,
				"59507": ExchangeError,
				"59508": AccountSuspended,
				"60001": AuthenticationError,
				"60002": AuthenticationError,
				"60003": AuthenticationError,
				"60004": AuthenticationError,
				"60005": AuthenticationError,
				"60006": InvalidNonce,
				"60007": AuthenticationError,
				"60008": AuthenticationError,
				"60009": AuthenticationError,
				"60010": AuthenticationError,
				"60011": AuthenticationError,
				"60012": BadRequest,
				"60013": BadRequest,
				"60014": RateLimitExceeded,
				"60015": NetworkError,
				"60016": ExchangeNotAvailable,
				"60017": BadRequest,
				"60018": BadRequest,
				"60019": BadRequest,
				"63999": ExchangeError,
				"70010": BadRequest,
				"70013": BadRequest,
				"70016": BadRequest,
			},
			"broad": map[string]interface{}{
				"Internal Server Error": ExchangeNotAvailable,
				"server error":          ExchangeNotAvailable,
			},
		},
		"precisionMode": TICK_SIZE,
		"options": map[string]interface{}{
			"fetchOHLCV": map[string]interface{}{
				"type": "Candles",
			},
			"createMarketBuyOrderRequiresPrice": true,
			"fetchMarkets":                      []interface{}{"spot"},
			"defaultType":                       "spot",
			"accountsByType": map[string]interface{}{
				"classic": "1",
				"spot":    "1",
				"funding": "6",
				"main":    "6",
				"unified": "18",
			},
			"accountsById": map[string]interface{}{
				"1":  "spot",
				"6":  "funding",
				"18": "unified",
			},
			"auth": map[string]interface{}{
				"time":                         "public",
				"currencies":                   "private",
				"instruments":                  "public",
				"rate":                         "public",
				"{instrument_id}/constituents": "public",
			},
			"warnOnFetchCurrenciesWithoutAuthorization": false,
			"defaultNetwork": "ERC20",
			"networks": map[string]interface{}{
				"ERC20": "Ethereum",
				"BTC":   "Bitcoin",
				"OMNI":  "Omni",
				"TRC20": "TRON",
			},
		},
		"commonCurrencies": map[string]interface{}{
			"AE":    "AET",
			"BOX":   "DefiBox",
			"HOT":   "Hydro Protocol",
			"HSR":   "HC",
			"MAG":   "Maggie",
			"SBTC":  "Super Bitcoin",
			"TRADE": "Unitrade",
			"YOYO":  "YOYOW",
			"WIN":   "WinToken",
		},
	})
}

/**
 * @method
 * @name okcoin#fetchTime
 * @description fetches the current integer timestamp in milliseconds from the exchange server
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {int} the current integer timestamp in milliseconds from the exchange server
 */
func (this *okcoin) FetchTime(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		response := (<-this.PublicGetPublicTime(params))
		PanicOnError(response)
		//
		// {
		//     "code": "0",
		//     "data":
		//         [
		//             {
		//                 "ts": "1737379360033"
		//             }
		//         ],
		//     "msg": ""
		// }
		//
		var data interface{} = this.SafeList(response, "data")
		var timestamp interface{} = this.SafeDict(data, 0)

		ch <- this.SafeInteger(timestamp, "ts")
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchMarkets
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-public-data-get-instruments
 * @description retrieves data on all markets for okcoin
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *okcoin) FetchMarkets(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{
			"instType": "SPOT",
		}

		response := (<-this.PublicGetPublicInstruments(this.Extend(request, params)))
		PanicOnError(response)
		var markets interface{} = this.SafeValue(response, "data", []interface{}{})

		ch <- this.ParseMarkets(markets)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseMarket(market interface{}) interface{} {
	//
	// spot markets
	//
	//     {
	//         "base_currency": "EOS",
	//         "instrument_id": "EOS-OKB",
	//         "min_size": "0.01",
	//         "quote_currency": "OKB",
	//         "size_increment": "0.000001",
	//         "tick_size": "0.0001"
	//     }
	//
	var id interface{} = this.SafeString(market, "instId")
	var typeVar interface{} = this.SafeStringLower(market, "instType")
	if IsTrue(IsEqual(typeVar, "futures")) {
		typeVar = "future"
	}
	var spot interface{} = (IsEqual(typeVar, "spot"))
	var future interface{} = (IsEqual(typeVar, "future"))
	var swap interface{} = (IsEqual(typeVar, "swap"))
	var option interface{} = (IsEqual(typeVar, "option"))
	var contract interface{} = IsTrue(IsTrue(swap) || IsTrue(future)) || IsTrue(option)
	var baseId interface{} = this.SafeString(market, "baseCcy")
	var quoteId interface{} = this.SafeString(market, "quoteCcy")
	var base interface{} = this.SafeCurrencyCode(baseId)
	var quote interface{} = this.SafeCurrencyCode(quoteId)
	var symbol interface{} = Add(Add(base, "/"), quote)
	var tickSize interface{} = this.SafeString(market, "tickSz")
	var fees interface{} = this.SafeValue2(this.Fees, typeVar, "trading", map[string]interface{}{})
	var maxLeverage interface{} = this.SafeString(market, "lever", "1")
	maxLeverage = Precise.StringMax(maxLeverage, "1")
	var maxSpotCost interface{} = this.SafeNumber(market, "maxMktSz")
	return this.Extend(fees, map[string]interface{}{
		"id":             id,
		"symbol":         symbol,
		"base":           base,
		"quote":          quote,
		"settle":         nil,
		"baseId":         baseId,
		"quoteId":        quoteId,
		"settleId":       nil,
		"type":           typeVar,
		"spot":           spot,
		"margin":         IsTrue(spot) && IsTrue((Precise.StringGt(maxLeverage, "1"))),
		"swap":           false,
		"future":         false,
		"option":         false,
		"active":         true,
		"contract":       false,
		"linear":         nil,
		"inverse":        nil,
		"contractSize":   Ternary(IsTrue(contract), this.SafeNumber(market, "ctVal"), nil),
		"expiry":         nil,
		"expiryDatetime": nil,
		"strike":         nil,
		"optionType":     nil,
		"created":        this.SafeInteger(market, "listTime"),
		"precision": map[string]interface{}{
			"amount": this.SafeNumber(market, "lotSz"),
			"price":  this.ParseNumber(tickSize),
		},
		"limits": map[string]interface{}{
			"leverage": map[string]interface{}{
				"min": this.ParseNumber("1"),
				"max": this.ParseNumber(maxLeverage),
			},
			"amount": map[string]interface{}{
				"min": this.SafeNumber(market, "minSz"),
				"max": nil,
			},
			"price": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"cost": map[string]interface{}{
				"min": nil,
				"max": Ternary(IsTrue(contract), nil, maxSpotCost),
			},
		},
		"info": market,
	})
}

/**
 * @method
 * @name okcoin#fetchCurrencies
 * @description fetches all available currencies on an exchange
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an associative dictionary of currencies
 */
func (this *okcoin) FetchCurrencies(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		if !IsTrue(this.CheckRequiredCredentials(false)) {
			if IsTrue(GetValue(this.Options, "warnOnFetchCurrenciesWithoutAuthorization")) {
				panic(ExchangeError(Add(this.Id, " fetchCurrencies() is a private API endpoint that requires authentication with API keys. Set the API keys on the exchange instance or exchange.options[\"warnOnFetchCurrenciesWithoutAuthorization\"] = false to suppress this warning message.")))
			}

			ch <- map[string]interface{}{}
			return nil
		} else {

			response := (<-this.PrivateGetAssetCurrencies(params))
			PanicOnError(response)
			var data interface{} = this.SafeList(response, "data", []interface{}{})
			var result interface{} = map[string]interface{}{}
			var dataByCurrencyId interface{} = this.GroupBy(data, "ccy")
			var currencyIds interface{} = ObjectKeys(dataByCurrencyId)
			for i := 0; IsLessThan(i, GetArrayLength(currencyIds)); i++ {
				var currencyId interface{} = GetValue(currencyIds, i)
				var code interface{} = this.SafeCurrencyCode(currencyId)
				var chains interface{} = GetValue(dataByCurrencyId, currencyId)
				var networks interface{} = map[string]interface{}{}
				for j := 0; IsLessThan(j, GetArrayLength(chains)); j++ {
					var chain interface{} = GetValue(chains, j)
					var networkId interface{} = this.SafeString(chain, "chain")
					if IsTrue(IsTrue((!IsEqual(networkId, nil))) && IsTrue((IsGreaterThanOrEqual(GetIndexOf(networkId, "-"), 0)))) {
						var parts interface{} = Split(networkId, "-")
						var chainPart interface{} = this.SafeString(parts, 1, networkId)
						var networkCode interface{} = this.NetworkIdToCode(chainPart)
						AddElementToObject(networks, networkCode, map[string]interface{}{
							"id":        networkId,
							"network":   networkCode,
							"active":    nil,
							"deposit":   this.SafeBool(chain, "canDep"),
							"withdraw":  this.SafeBool(chain, "canWd"),
							"fee":       this.SafeNumber(chain, "minFee"),
							"precision": this.ParseNumber(this.ParsePrecision(this.SafeString(chain, "wdTickSz"))),
							"limits": map[string]interface{}{
								"withdraw": map[string]interface{}{
									"min": this.SafeNumber(chain, "minWd"),
									"max": this.SafeNumber(chain, "maxWd"),
								},
							},
							"info": chain,
						})
					}
				}
				var firstChain interface{} = this.SafeValue(chains, 0)
				AddElementToObject(result, code, this.SafeCurrencyStructure(map[string]interface{}{
					"info":      chains,
					"code":      code,
					"id":        currencyId,
					"name":      this.SafeString(firstChain, "name"),
					"active":    nil,
					"deposit":   nil,
					"withdraw":  nil,
					"fee":       nil,
					"precision": nil,
					"limits": map[string]interface{}{
						"amount": map[string]interface{}{
							"min": nil,
							"max": nil,
						},
					},
					"networks": networks,
				}))
			}

			ch <- result
			return nil
		}

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchOrderBook
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-order-book
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *okcoin) FetchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes9028 := (<-this.LoadMarkets())
		PanicOnError(retRes9028)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
		}
		limit = Ternary(IsTrue((IsEqual(limit, nil))), 20, limit)
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "sz", limit) // max 400
		}

		response := (<-this.PublicGetMarketBooks(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "asks": [
		//                     ["0.07228","4.211619","0","2"], // price, amount, liquidated orders, total open orders
		//                     ["0.0723","299.880364","0","2"],
		//                     ["0.07231","3.72832","0","1"],
		//                 ],
		//                 "bids": [
		//                     ["0.07221","18.5","0","1"],
		//                     ["0.0722","18.5","0","1"],
		//                     ["0.07219","0.505407","0","1"],
		//                 ],
		//                 "ts": "1621438475342"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var first interface{} = this.SafeValue(data, 0, map[string]interface{}{})
		var timestamp interface{} = this.SafeInteger(first, "ts")

		ch <- this.ParseOrderBook(first, symbol, timestamp)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "instType": "SPOT",
	//         "instId": "ETH-BTC",
	//         "last": "0.07319",
	//         "lastSz": "0.044378",
	//         "askPx": "0.07322",
	//         "askSz": "4.2",
	//         "bidPx": "0.0732",
	//         "bidSz": "6.050058",
	//         "open24h": "0.07801",
	//         "high24h": "0.07975",
	//         "low24h": "0.06019",
	//         "volCcy24h": "11788.887619",
	//         "vol24h": "167493.829229",
	//         "ts": "1621440583784",
	//         "sodUtc0": "0.07872",
	//         "sodUtc8": "0.07345"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var timestamp interface{} = this.SafeInteger(ticker, "ts")
	var marketId interface{} = this.SafeString(ticker, "instId")
	market = this.SafeMarket(marketId, market, "-")
	var symbol interface{} = GetValue(market, "symbol")
	var last interface{} = this.SafeString(ticker, "last")
	var open interface{} = this.SafeString(ticker, "open24h")
	var spot interface{} = this.SafeBool(market, "spot", false)
	var quoteVolume interface{} = Ternary(IsTrue(spot), this.SafeString(ticker, "volCcy24h"), nil)
	var baseVolume interface{} = this.SafeString(ticker, "vol24h")
	var high interface{} = this.SafeString(ticker, "high24h")
	var low interface{} = this.SafeString(ticker, "low24h")
	return this.SafeTicker(map[string]interface{}{
		"symbol":        symbol,
		"timestamp":     timestamp,
		"datetime":      this.Iso8601(timestamp),
		"high":          high,
		"low":           low,
		"bid":           this.SafeString(ticker, "bidPx"),
		"bidVolume":     this.SafeString(ticker, "bidSz"),
		"ask":           this.SafeString(ticker, "askPx"),
		"askVolume":     this.SafeString(ticker, "askSz"),
		"vwap":          nil,
		"open":          open,
		"close":         last,
		"last":          last,
		"previousClose": nil,
		"change":        nil,
		"percentage":    nil,
		"average":       nil,
		"baseVolume":    baseVolume,
		"quoteVolume":   quoteVolume,
		"info":          ticker,
	}, market)
}

/**
 * @method
 * @name okcoin#fetchTicker
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-ticker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *okcoin) FetchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes10058 := (<-this.LoadMarkets())
		PanicOnError(retRes10058)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
		}

		response := (<-this.PublicGetMarketTicker(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var first interface{} = this.SafeValue(data, 0, map[string]interface{}{})

		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "instType": "SPOT",
		//                 "instId": "ETH-BTC",
		//                 "last": "0.07319",
		//                 "lastSz": "0.044378",
		//                 "askPx": "0.07322",
		//                 "askSz": "4.2",
		//                 "bidPx": "0.0732",
		//                 "bidSz": "6.050058",
		//                 "open24h": "0.07801",
		//                 "high24h": "0.07975",
		//                 "low24h": "0.06019",
		//                 "volCcy24h": "11788.887619",
		//                 "vol24h": "167493.829229",
		//                 "ts": "1621440583784",
		//                 "sodUtc0": "0.07872",
		//                 "sodUtc8": "0.07345"
		//             }
		//         ]
		//     }
		//
		ch <- this.ParseTicker(first, market)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchTickers
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-tickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *okcoin) FetchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		symbols = this.MarketSymbols(symbols)
		var request interface{} = map[string]interface{}{
			"instType": "SPOT",
		}

		response := (<-this.PublicGetMarketTickers(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTickers(data, symbols, params)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	//
	// public fetchTrades
	//
	//     {
	//         "instId": "ETH-BTC",
	//         "side": "sell",
	//         "sz": "0.119501",
	//         "px": "0.07065",
	//         "tradeId": "15826757",
	//         "ts": "1621446178316"
	//     }
	//
	// private fetchMyTrades
	//
	//     {
	//         "side": "buy",
	//         "fillSz": "0.007533",
	//         "fillPx": "2654.98",
	//         "fee": "-0.000007533",
	//         "ordId": "317321390244397056",
	//         "instType": "SPOT",
	//         "instId": "ETH-USDT",
	//         "clOrdId": "",
	//         "posSide": "net",
	//         "billId": "317321390265368576",
	//         "tag": "0",
	//         "execType": "T",
	//         "tradeId": "107601752",
	//         "feeCcy": "ETH",
	//         "ts": "1621927314985"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var id interface{} = this.SafeString(trade, "tradeId")
	var marketId interface{} = this.SafeString(trade, "instId")
	market = this.SafeMarket(marketId, market, "-")
	var symbol interface{} = GetValue(market, "symbol")
	var timestamp interface{} = this.SafeInteger(trade, "ts")
	var price interface{} = this.SafeString2(trade, "fillPx", "px")
	var amount interface{} = this.SafeString2(trade, "fillSz", "sz")
	var side interface{} = this.SafeString(trade, "side")
	var orderId interface{} = this.SafeString(trade, "ordId")
	var feeCostString interface{} = this.SafeString(trade, "fee")
	var fee interface{} = nil
	if IsTrue(!IsEqual(feeCostString, nil)) {
		var feeCostSigned interface{} = Precise.StringNeg(feeCostString)
		var feeCurrencyId interface{} = this.SafeString(trade, "feeCcy")
		var feeCurrencyCode interface{} = this.SafeCurrencyCode(feeCurrencyId)
		fee = map[string]interface{}{
			"cost":     feeCostSigned,
			"currency": feeCurrencyCode,
		}
	}
	var takerOrMaker interface{} = this.SafeString(trade, "execType")
	if IsTrue(IsEqual(takerOrMaker, "T")) {
		takerOrMaker = "taker"
	} else if IsTrue(IsEqual(takerOrMaker, "M")) {
		takerOrMaker = "maker"
	}
	return this.SafeTrade(map[string]interface{}{
		"info":         trade,
		"timestamp":    timestamp,
		"datetime":     this.Iso8601(timestamp),
		"symbol":       symbol,
		"id":           id,
		"order":        orderId,
		"type":         nil,
		"takerOrMaker": takerOrMaker,
		"side":         side,
		"price":        price,
		"amount":       amount,
		"cost":         nil,
		"fee":          fee,
	}, market)
}

/**
 * @method
 * @name okcoin#fetchTrades
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-trades
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-trades-history
 * @description get the list of most recent trades for a particular symbol
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *okcoin) FetchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes11508 := (<-this.LoadMarkets())
		PanicOnError(retRes11508)
		var market interface{} = this.Market(symbol)
		if IsTrue(IsTrue((IsEqual(limit, nil))) || IsTrue((IsGreaterThan(limit, 100)))) {
			limit = 100 // maximum = default = 100
		}
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
		}
		var method interface{} = nil
		methodparamsVariable := this.HandleOptionAndParams(params, "fetchTrades", "method", "publicGetMarketTrades")
		method = GetValue(methodparamsVariable, 0)
		params = GetValue(methodparamsVariable, 1)
		var response interface{} = nil
		if IsTrue(IsEqual(method, "publicGetMarketTrades")) {

			response = (<-this.PublicGetMarketTrades(this.Extend(request, params)))
			PanicOnError(response)
		} else {

			response = (<-this.PublicGetMarketHistoryTrades(this.Extend(request, params)))
			PanicOnError(response)
		}
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTrades(data, market, since, limit)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseOHLCV(ohlcv interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     [
	//         "1678928760000", // timestamp
	//         "24341.4", // open
	//         "24344", // high
	//         "24313.2", // low
	//         "24323", // close
	//         "628", // contract volume
	//         "2.5819", // base volume
	//         "62800", // quote volume
	//         "0" // candlestick state
	//     ]
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	return []interface{}{this.SafeInteger(ohlcv, 0), this.SafeNumber(ohlcv, 1), this.SafeNumber(ohlcv, 2), this.SafeNumber(ohlcv, 3), this.SafeNumber(ohlcv, 4), this.SafeNumber(ohlcv, 5)}
}

/**
 * @method
 * @name okcoin#fetchOHLCV
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-candlesticks
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-market-data-get-candlesticks-history
 * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
 * @param {string} timeframe the length of time each candle represents
 * @param {int} [since] timestamp in ms of the earliest candle to fetch
 * @param {int} [limit] the maximum amount of candles to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
 */
func (this *okcoin) FetchOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes12088 := (<-this.LoadMarkets())
		PanicOnError(retRes12088)
		var market interface{} = this.Market(symbol)
		var duration interface{} = this.ParseTimeframe(timeframe)
		var options interface{} = this.SafeValue(this.Options, "fetchOHLCV", map[string]interface{}{})
		var bar interface{} = this.SafeString(this.Timeframes, timeframe, timeframe)
		var timezone interface{} = this.SafeString(options, "timezone", "UTC")
		if IsTrue(IsTrue((IsEqual(timezone, "UTC"))) && IsTrue((IsGreaterThanOrEqual(duration, 21600)))) {
			bar = Add(bar, ToLower(timezone))
		}
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
			"bar":    bar,
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit) // default 100, max 100
		}
		var method interface{} = nil
		methodparamsVariable := this.HandleOptionAndParams(params, "fetchOHLCV", "method", "publicGetMarketCandles")
		method = GetValue(methodparamsVariable, 0)
		params = GetValue(methodparamsVariable, 1)
		var response interface{} = nil
		if IsTrue(IsEqual(method, "publicGetMarketCandles")) {

			response = (<-this.PublicGetMarketCandles(this.Extend(request, params)))
			PanicOnError(response)
		} else {

			response = (<-this.PublicGetMarketHistoryCandles(this.Extend(request, params)))
			PanicOnError(response)
		}
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOHLCVs(data, market, timeframe, since, limit)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseAccountBalance(response interface{}) interface{} {
	//
	// account
	//
	//     [
	//         {
	//             "balance":  0,
	//             "available":  0,
	//             "currency": "BTC",
	//             "hold":  0
	//         },
	//         {
	//             "balance":  0,
	//             "available":  0,
	//             "currency": "ETH",
	//             "hold":  0
	//         }
	//     ]
	//
	// spot
	//
	//     [
	//         {
	//             "frozen": "0",
	//             "hold": "0",
	//             "id": "2149632",
	//             "currency": "BTC",
	//             "balance": "0.****************",
	//             "available": "0.****************",
	//             "holds": "0"
	//         },
	//         {
	//             "frozen": "0",
	//             "hold": "0",
	//             "id": "2149632",
	//             "currency": "ICN",
	//             "balance": "0.***********",
	//             "available": "0.***********",
	//             "holds": "0"
	//         }
	//     ]
	//
	var result interface{} = map[string]interface{}{
		"info":      response,
		"timestamp": nil,
		"datetime":  nil,
	}
	for i := 0; IsLessThan(i, GetArrayLength(response)); i++ {
		var balance interface{} = GetValue(response, i)
		var currencyId interface{} = this.SafeString(balance, "currency")
		var code interface{} = this.SafeCurrencyCode(currencyId)
		var account interface{} = this.Account()
		AddElementToObject(account, "total", this.SafeString(balance, "balance"))
		AddElementToObject(account, "used", this.SafeString(balance, "hold"))
		AddElementToObject(account, "free", this.SafeString(balance, "available"))
		AddElementToObject(result, code, account)
	}
	return this.SafeBalance(result)
}

/**
 * @method
 * @name okcoin#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *okcoin) FetchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes13048 := (<-this.LoadMarkets())
		PanicOnError(retRes13048)
		marketTypequeryVariable := this.HandleMarketTypeAndParams("fetchBalance", nil, params)
		marketType := GetValue(marketTypequeryVariable, 0)
		query := GetValue(marketTypequeryVariable, 1)
		var request interface{} = map[string]interface{}{}
		var response interface{} = nil
		if IsTrue(IsEqual(marketType, "funding")) {

			response = (<-this.PrivateGetAssetBalances(this.Extend(request, query)))
			PanicOnError(response)
		} else {

			response = (<-this.PrivateGetAccountBalance(this.Extend(request, query)))
			PanicOnError(response)
		}
		//
		//  {
		//         "code": "0",
		//         "data": [
		//             {
		//                 "category": "1",
		//                 "delivery": "",
		//                 "exercise": "",
		//                 "instType": "SPOT",
		//                 "level": "Lv1",
		//                 "maker": "-0.0008",
		//                 "taker": "-0.001",
		//                 "ts": "*************"
		//             }
		//         ],
		//         "msg": ""
		//     }
		//
		if IsTrue(IsEqual(marketType, "funding")) {

			ch <- this.ParseFundingBalance(response)
			return nil
		} else {

			ch <- this.ParseTradingBalance(response)
			return nil
		}

	}()
	return ch
}
func (this *okcoin) ParseTradingBalance(response interface{}) interface{} {
	var result interface{} = map[string]interface{}{
		"info": response,
	}
	var data interface{} = this.SafeValue(response, "data", []interface{}{})
	var first interface{} = this.SafeValue(data, 0, map[string]interface{}{})
	var timestamp interface{} = this.SafeInteger(first, "uTime")
	var details interface{} = this.SafeValue(first, "details", []interface{}{})
	for i := 0; IsLessThan(i, GetArrayLength(details)); i++ {
		var balance interface{} = GetValue(details, i)
		var currencyId interface{} = this.SafeString(balance, "ccy")
		var code interface{} = this.SafeCurrencyCode(currencyId)
		var account interface{} = this.Account()
		// it may be incorrect to use total, free and used for swap accounts
		var eq interface{} = this.SafeString(balance, "eq")
		var availEq interface{} = this.SafeString(balance, "availEq")
		if IsTrue(IsTrue((IsEqual(eq, nil))) || IsTrue((IsEqual(availEq, nil)))) {
			AddElementToObject(account, "free", this.SafeString(balance, "availBal"))
			AddElementToObject(account, "used", this.SafeString(balance, "frozenBal"))
		} else {
			AddElementToObject(account, "total", eq)
			AddElementToObject(account, "free", availEq)
		}
		AddElementToObject(result, code, account)
	}
	AddElementToObject(result, "timestamp", timestamp)
	AddElementToObject(result, "datetime", this.Iso8601(timestamp))
	return this.SafeBalance(result)
}
func (this *okcoin) ParseFundingBalance(response interface{}) interface{} {
	var result interface{} = map[string]interface{}{
		"info": response,
	}
	var data interface{} = this.SafeValue(response, "data", []interface{}{})
	for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
		var balance interface{} = GetValue(data, i)
		var currencyId interface{} = this.SafeString(balance, "ccy")
		var code interface{} = this.SafeCurrencyCode(currencyId)
		var account interface{} = this.Account()
		// it may be incorrect to use total, free and used for swap accounts
		AddElementToObject(account, "total", this.SafeString(balance, "bal"))
		AddElementToObject(account, "free", this.SafeString(balance, "availBal"))
		AddElementToObject(account, "used", this.SafeString(balance, "frozenBal"))
		AddElementToObject(result, code, account)
	}
	return this.SafeBalance(result)
}

/**
 * @method
 * @name okcoin#createMarketBuyOrderWithCost
 * @description create a market buy order by providing the symbol and cost
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-place-order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {float} cost how much you want to trade in units of the quote currency
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) CreateMarketBuyOrderWithCost(symbol interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes13968 := (<-this.LoadMarkets())
		PanicOnError(retRes13968)
		var market interface{} = this.Market(symbol)
		if !IsTrue(GetValue(market, "spot")) {
			panic(NotSupported(Add(this.Id, " createMarketBuyOrderWithCost() supports spot orders only")))
		}
		AddElementToObject(params, "createMarketBuyOrderRequiresPrice", false)
		AddElementToObject(params, "tgtCcy", "quote_ccy")

		retRes140315 := (<-this.CreateOrder(symbol, "market", "buy", cost, nil, params))
		PanicOnError(retRes140315)
		ch <- retRes140315
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#createOrder
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-place-order
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-place-algo-order
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-place-multiple-orders
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-advance-algo-order
 * @description create a trade order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} price the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {bool} [params.reduceOnly] MARGIN orders only, or swap/future orders in net mode
 * @param {bool} [params.postOnly] true to place a post only order
 * @param {float} [params.triggerPrice] conditional orders only, the price at which the order is to be triggered
 * @param {object} [params.takeProfit] *takeProfit object in params* containing the triggerPrice at which the attached take profit order will be triggered (perpetual swap markets only)
 * @param {float} [params.takeProfit.triggerPrice] take profit trigger price
 * @param {float} [params.takeProfit.price] used for take profit limit orders, not used for take profit market price orders
 * @param {string} [params.takeProfit.type] 'market' or 'limit' used to specify the take profit price type
 * @param {object} [params.stopLoss] *stopLoss object in params* containing the triggerPrice at which the attached stop loss order will be triggered (perpetual swap markets only)
 * @param {float} [params.stopLoss.triggerPrice] stop loss trigger price
 * @param {float} [params.stopLoss.price] used for stop loss limit orders, not used for stop loss market price orders
 * @param {string} [params.stopLoss.type] 'market' or 'limit' used to specify the stop loss price type
 * @param {float} [params.cost] *spot market buy only* the quote quantity that can be used as an alternative for the amount
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) CreateOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes14358 := (<-this.LoadMarkets())
		PanicOnError(retRes14358)
		var market interface{} = this.Market(symbol)
		var request interface{} = this.CreateOrderRequest(symbol, typeVar, side, amount, price, params)
		var method interface{} = this.SafeString(this.Options, "createOrder", "privatePostTradeBatchOrders")
		var requestOrdType interface{} = this.SafeString(request, "ordType")
		if IsTrue(IsTrue(IsTrue(IsTrue(IsTrue(IsTrue((IsEqual(requestOrdType, "trigger"))) || IsTrue((IsEqual(requestOrdType, "conditional")))) || IsTrue((IsEqual(typeVar, "oco")))) || IsTrue((IsEqual(typeVar, "move_order_stop")))) || IsTrue((IsEqual(typeVar, "iceberg")))) || IsTrue((IsEqual(typeVar, "twap")))) {
			method = "privatePostTradeOrderAlgo"
		}
		if IsTrue(IsEqual(method, "privatePostTradeBatchOrders")) {
			// keep the request body the same
			// submit a single order in an array to the batch order endpoint
			// because it has a lower ratelimit
			request = []interface{}{request}
		}
		var response interface{} = nil
		if IsTrue(IsEqual(method, "privatePostTradeOrder")) {

			response = (<-this.PrivatePostTradeOrder(request))
			PanicOnError(response)
		} else if IsTrue(IsEqual(method, "privatePostTradeOrderAlgo")) {

			response = (<-this.PrivatePostTradeOrderAlgo(request))
			PanicOnError(response)
		} else if IsTrue(IsEqual(method, "privatePostTradeBatchOrders")) {

			response = (<-this.PrivatePostTradeBatchOrders(request))
			PanicOnError(response)
		} else {
			panic(ExchangeError(Add(this.Id, " createOrder() this.options[\"createOrder\"] must be either privatePostTradeBatchOrders or privatePostTradeOrder or privatePostTradeOrderAlgo")))
		}
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var first interface{} = this.SafeValue(data, 0)
		var order interface{} = this.ParseOrder(first, market)
		AddElementToObject(order, "type", typeVar)
		AddElementToObject(order, "side", side)

		ch <- order
		return nil

	}()
	return ch
}
func (this *okcoin) CreateOrderRequest(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) interface{} {
	price := GetArg(optionalArgs, 0, nil)
	_ = price
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var market interface{} = this.Market(symbol)
	var request interface{} = map[string]interface{}{
		"instId":  GetValue(market, "id"),
		"side":    side,
		"ordType": typeVar,
	}
	var triggerPrice interface{} = this.SafeValueN(params, []interface{}{"triggerPrice", "stopPrice", "triggerPx"})
	var timeInForce interface{} = this.SafeString(params, "timeInForce", "GTC")
	var takeProfitPrice interface{} = this.SafeValue2(params, "takeProfitPrice", "tpTriggerPx")
	var tpOrdPx interface{} = this.SafeValue(params, "tpOrdPx", price)
	var tpTriggerPxType interface{} = this.SafeString(params, "tpTriggerPxType", "last")
	var stopLossPrice interface{} = this.SafeValue2(params, "stopLossPrice", "slTriggerPx")
	var slOrdPx interface{} = this.SafeValue(params, "slOrdPx", price)
	var slTriggerPxType interface{} = this.SafeString(params, "slTriggerPxType", "last")
	var clientOrderId interface{} = this.SafeString2(params, "clOrdId", "clientOrderId")
	var stopLoss interface{} = this.SafeValue(params, "stopLoss")
	var stopLossDefined interface{} = (!IsEqual(stopLoss, nil))
	var takeProfit interface{} = this.SafeValue(params, "takeProfit")
	var takeProfitDefined interface{} = (!IsEqual(takeProfit, nil))
	var defaultMarginMode interface{} = this.SafeString2(this.Options, "defaultMarginMode", "marginMode", "cross")
	var marginMode interface{} = this.SafeString2(params, "marginMode", "tdMode") // cross or isolated, tdMode not ommited so as to be extended into the request
	var margin interface{} = false
	if IsTrue(IsTrue((!IsEqual(marginMode, nil))) && IsTrue((!IsEqual(marginMode, "cash")))) {
		margin = true
	} else {
		marginMode = defaultMarginMode
		margin = this.SafeBool(params, "margin", false)
	}
	if IsTrue(margin) {
		var defaultCurrency interface{} = Ternary(IsTrue((IsEqual(side, "buy"))), GetValue(market, "quote"), GetValue(market, "base"))
		var currency interface{} = this.SafeString(params, "ccy", defaultCurrency)
		AddElementToObject(request, "ccy", this.SafeCurrencyCode(currency))
	}
	var tradeMode interface{} = Ternary(IsTrue(margin), marginMode, "cash")
	AddElementToObject(request, "tdMode", tradeMode)
	var isMarketOrder interface{} = IsEqual(typeVar, "market")
	var postOnly interface{} = false
	postOnlyparamsVariable := this.HandlePostOnly(isMarketOrder, IsEqual(typeVar, "post_only"), params)
	postOnly = GetValue(postOnlyparamsVariable, 0)
	params = GetValue(postOnlyparamsVariable, 1)
	params = this.Omit(params, []interface{}{"currency", "ccy", "marginMode", "timeInForce", "stopPrice", "triggerPrice", "clientOrderId", "stopLossPrice", "takeProfitPrice", "slOrdPx", "tpOrdPx", "margin", "stopLoss", "takeProfit"})
	var ioc interface{} = IsTrue((IsEqual(timeInForce, "IOC"))) || IsTrue((IsEqual(typeVar, "ioc")))
	var fok interface{} = IsTrue((IsEqual(timeInForce, "FOK"))) || IsTrue((IsEqual(typeVar, "fok")))
	var trigger interface{} = IsTrue((!IsEqual(triggerPrice, nil))) || IsTrue((IsEqual(typeVar, "trigger")))
	var conditional interface{} = IsTrue(IsTrue((!IsEqual(stopLossPrice, nil))) || IsTrue((!IsEqual(takeProfitPrice, nil)))) || IsTrue((IsEqual(typeVar, "conditional")))
	var marketIOC interface{} = IsTrue((IsTrue(isMarketOrder) && IsTrue(ioc))) || IsTrue((IsEqual(typeVar, "optimal_limit_ioc")))
	var defaultTgtCcy interface{} = this.SafeString(this.Options, "tgtCcy", "base_ccy")
	var tgtCcy interface{} = this.SafeString(params, "tgtCcy", defaultTgtCcy)
	if IsTrue((!IsTrue(margin))) {
		AddElementToObject(request, "tgtCcy", tgtCcy)
	}
	if IsTrue(IsTrue(isMarketOrder) || IsTrue(marketIOC)) {
		AddElementToObject(request, "ordType", "market")
		if IsTrue(IsEqual(side, "buy")) {
			// spot market buy: "sz" can refer either to base currency units or to quote currency units
			// see documentation: https://www.okx.com/docs-v5/en/#rest-api-trade-place-order
			if IsTrue(IsEqual(tgtCcy, "quote_ccy")) {
				// quote_ccy: sz refers to units of quote currency
				var quoteAmount interface{} = nil
				var createMarketBuyOrderRequiresPrice interface{} = true
				createMarketBuyOrderRequiresPriceparamsVariable := this.HandleOptionAndParams(params, "createOrder", "createMarketBuyOrderRequiresPrice", true)
				createMarketBuyOrderRequiresPrice = GetValue(createMarketBuyOrderRequiresPriceparamsVariable, 0)
				params = GetValue(createMarketBuyOrderRequiresPriceparamsVariable, 1)
				var cost interface{} = this.SafeNumber2(params, "cost", "sz")
				params = this.Omit(params, []interface{}{"cost", "sz"})
				if IsTrue(!IsEqual(cost, nil)) {
					quoteAmount = this.CostToPrecision(symbol, cost)
				} else if IsTrue(createMarketBuyOrderRequiresPrice) {
					if IsTrue(IsEqual(price, nil)) {
						panic(InvalidOrder(Add(this.Id, " createOrder() requires the price argument for market buy orders to calculate the total cost to spend (amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to false and pass the cost to spend (quote quantity) in the amount argument")))
					} else {
						var amountString interface{} = this.NumberToString(amount)
						var priceString interface{} = this.NumberToString(price)
						var costRequest interface{} = Precise.StringMul(amountString, priceString)
						quoteAmount = this.CostToPrecision(symbol, costRequest)
					}
				} else {
					quoteAmount = this.CostToPrecision(symbol, amount)
				}
				AddElementToObject(request, "sz", quoteAmount)
			} else {
				AddElementToObject(request, "sz", this.AmountToPrecision(symbol, amount))
			}
		} else {
			AddElementToObject(request, "sz", this.AmountToPrecision(symbol, amount))
		}
	} else {
		AddElementToObject(request, "sz", this.AmountToPrecision(symbol, amount))
		if IsTrue(IsTrue((!IsTrue(trigger))) && IsTrue((!IsTrue(conditional)))) {
			AddElementToObject(request, "px", this.PriceToPrecision(symbol, price))
		}
	}
	if IsTrue(postOnly) {
		AddElementToObject(request, "ordType", "post_only")
	} else if IsTrue(IsTrue(ioc) && !IsTrue(marketIOC)) {
		AddElementToObject(request, "ordType", "ioc")
	} else if IsTrue(fok) {
		AddElementToObject(request, "ordType", "fok")
	} else if IsTrue(IsTrue(stopLossDefined) || IsTrue(takeProfitDefined)) {
		if IsTrue(stopLossDefined) {
			var stopLossTriggerPrice interface{} = this.SafeValueN(stopLoss, []interface{}{"triggerPrice", "stopPrice", "slTriggerPx"})
			if IsTrue(IsEqual(stopLossTriggerPrice, nil)) {
				panic(InvalidOrder(Add(this.Id, " createOrder() requires a trigger price in params[\"stopLoss\"][\"triggerPrice\"] for a stop loss order")))
			}
			AddElementToObject(request, "slTriggerPx", this.PriceToPrecision(symbol, stopLossTriggerPrice))
			var stopLossLimitPrice interface{} = this.SafeValueN(stopLoss, []interface{}{"price", "stopLossPrice", "slOrdPx"})
			var stopLossOrderType interface{} = this.SafeString(stopLoss, "type")
			if IsTrue(!IsEqual(stopLossOrderType, nil)) {
				var stopLossLimitOrderType interface{} = (IsEqual(stopLossOrderType, "limit"))
				var stopLossMarketOrderType interface{} = (IsEqual(stopLossOrderType, "market"))
				if IsTrue(IsTrue((!IsTrue(stopLossLimitOrderType))) && IsTrue((!IsTrue(stopLossMarketOrderType)))) {
					panic(InvalidOrder(Add(this.Id, " createOrder() params[\"stopLoss\"][\"type\"] must be either \"limit\" or \"market\"")))
				} else if IsTrue(stopLossLimitOrderType) {
					if IsTrue(IsEqual(stopLossLimitPrice, nil)) {
						panic(InvalidOrder(Add(this.Id, " createOrder() requires a limit price in params[\"stopLoss\"][\"price\"] for a stop loss limit order")))
					} else {
						AddElementToObject(request, "slOrdPx", this.PriceToPrecision(symbol, stopLossLimitPrice))
					}
				} else if IsTrue(IsEqual(stopLossOrderType, "market")) {
					AddElementToObject(request, "slOrdPx", "-1")
				}
			} else if IsTrue(!IsEqual(stopLossLimitPrice, nil)) {
				AddElementToObject(request, "slOrdPx", this.PriceToPrecision(symbol, stopLossLimitPrice)) // limit sl order
			} else {
				AddElementToObject(request, "slOrdPx", "-1") // market sl order
			}
			var stopLossTriggerPriceType interface{} = this.SafeString2(stopLoss, "triggerPriceType", "slTriggerPxType", "last")
			if IsTrue(!IsEqual(stopLossTriggerPriceType, nil)) {
				if IsTrue(IsTrue(IsTrue((!IsEqual(stopLossTriggerPriceType, "last"))) && IsTrue((!IsEqual(stopLossTriggerPriceType, "index")))) && IsTrue((!IsEqual(stopLossTriggerPriceType, "mark")))) {
					panic(InvalidOrder(Add(this.Id, " createOrder() stop loss trigger price type must be one of \"last\", \"index\" or \"mark\"")))
				}
				AddElementToObject(request, "slTriggerPxType", stopLossTriggerPriceType)
			}
		}
		if IsTrue(takeProfitDefined) {
			var takeProfitTriggerPrice interface{} = this.SafeValueN(takeProfit, []interface{}{"triggerPrice", "stopPrice", "tpTriggerPx"})
			if IsTrue(IsEqual(takeProfitTriggerPrice, nil)) {
				panic(InvalidOrder(Add(this.Id, " createOrder() requires a trigger price in params[\"takeProfit\"][\"triggerPrice\"], or params[\"takeProfit\"][\"stopPrice\"], or params[\"takeProfit\"][\"tpTriggerPx\"] for a take profit order")))
			}
			AddElementToObject(request, "tpTriggerPx", this.PriceToPrecision(symbol, takeProfitTriggerPrice))
			var takeProfitLimitPrice interface{} = this.SafeValueN(takeProfit, []interface{}{"price", "takeProfitPrice", "tpOrdPx"})
			var takeProfitOrderType interface{} = this.SafeString(takeProfit, "type")
			if IsTrue(!IsEqual(takeProfitOrderType, nil)) {
				var takeProfitLimitOrderType interface{} = (IsEqual(takeProfitOrderType, "limit"))
				var takeProfitMarketOrderType interface{} = (IsEqual(takeProfitOrderType, "market"))
				if IsTrue(IsTrue((!IsTrue(takeProfitLimitOrderType))) && IsTrue((!IsTrue(takeProfitMarketOrderType)))) {
					panic(InvalidOrder(Add(this.Id, " createOrder() params[\"takeProfit\"][\"type\"] must be either \"limit\" or \"market\"")))
				} else if IsTrue(takeProfitLimitOrderType) {
					if IsTrue(IsEqual(takeProfitLimitPrice, nil)) {
						panic(InvalidOrder(Add(this.Id, " createOrder() requires a limit price in params[\"takeProfit\"][\"price\"] or params[\"takeProfit\"][\"tpOrdPx\"] for a take profit limit order")))
					} else {
						AddElementToObject(request, "tpOrdPx", this.PriceToPrecision(symbol, takeProfitLimitPrice))
					}
				} else if IsTrue(IsEqual(takeProfitOrderType, "market")) {
					AddElementToObject(request, "tpOrdPx", "-1")
				}
			} else if IsTrue(!IsEqual(takeProfitLimitPrice, nil)) {
				AddElementToObject(request, "tpOrdPx", this.PriceToPrecision(symbol, takeProfitLimitPrice)) // limit tp order
			} else {
				AddElementToObject(request, "tpOrdPx", "-1") // market tp order
			}
			var takeProfitTriggerPriceType interface{} = this.SafeString2(takeProfit, "triggerPriceType", "tpTriggerPxType", "last")
			if IsTrue(!IsEqual(takeProfitTriggerPriceType, nil)) {
				if IsTrue(IsTrue(IsTrue((!IsEqual(takeProfitTriggerPriceType, "last"))) && IsTrue((!IsEqual(takeProfitTriggerPriceType, "index")))) && IsTrue((!IsEqual(takeProfitTriggerPriceType, "mark")))) {
					panic(InvalidOrder(Add(this.Id, " createOrder() take profit trigger price type must be one of \"last\", \"index\" or \"mark\"")))
				}
				AddElementToObject(request, "tpTriggerPxType", takeProfitTriggerPriceType)
			}
		}
	} else if IsTrue(trigger) {
		AddElementToObject(request, "ordType", "trigger")
		AddElementToObject(request, "triggerPx", this.PriceToPrecision(symbol, triggerPrice))
		AddElementToObject(request, "orderPx", Ternary(IsTrue(isMarketOrder), "-1", this.PriceToPrecision(symbol, price)))
	} else if IsTrue(conditional) {
		AddElementToObject(request, "ordType", "conditional")
		var twoWayCondition interface{} = (IsTrue((!IsEqual(takeProfitPrice, nil))) && IsTrue((!IsEqual(stopLossPrice, nil))))
		// if TP and SL are sent together
		// as ordType 'conditional' only stop-loss order will be applied
		if IsTrue(twoWayCondition) {
			AddElementToObject(request, "ordType", "oco")
		}
		if IsTrue(!IsEqual(takeProfitPrice, nil)) {
			AddElementToObject(request, "tpTriggerPx", this.PriceToPrecision(symbol, takeProfitPrice))
			AddElementToObject(request, "tpOrdPx", Ternary(IsTrue((IsEqual(tpOrdPx, nil))), "-1", this.PriceToPrecision(symbol, tpOrdPx)))
			AddElementToObject(request, "tpTriggerPxType", tpTriggerPxType)
		}
		if IsTrue(!IsEqual(stopLossPrice, nil)) {
			AddElementToObject(request, "slTriggerPx", this.PriceToPrecision(symbol, stopLossPrice))
			AddElementToObject(request, "slOrdPx", Ternary(IsTrue((IsEqual(slOrdPx, nil))), "-1", this.PriceToPrecision(symbol, slOrdPx)))
			AddElementToObject(request, "slTriggerPxType", slTriggerPxType)
		}
	}
	if IsTrue(IsEqual(clientOrderId, nil)) {
		var brokerId interface{} = this.SafeString(this.Options, "brokerId")
		if IsTrue(!IsEqual(brokerId, nil)) {
			AddElementToObject(request, "clOrdId", Add(brokerId, this.Uuid16()))
			AddElementToObject(request, "tag", brokerId)
		}
	} else {
		AddElementToObject(request, "clOrdId", clientOrderId)
		params = this.Omit(params, []interface{}{"clOrdId", "clientOrderId"})
	}
	return this.Extend(request, params)
}

/**
 * @method
 * @name okcoin#cancelOrder
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-order
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-algo-order
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-advance-algo-order
 * @description cancels an open order
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {bool} [params.trigger] True if cancel trigger or conditional orders
 * @param {bool} [params.advanced] True if canceling advanced orders only
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) CancelOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " cancelOrder() requires a symbol argument")))
		}

		retRes17088 := (<-this.LoadMarkets())
		PanicOnError(retRes17088)
		var trigger interface{} = this.SafeValue2(params, "stop", "trigger")
		var advanced interface{} = this.SafeValue(params, "advanced")
		if IsTrue(IsTrue(trigger) || IsTrue(advanced)) {

			orderInner := (<-this.CancelOrders([]interface{}{id}, symbol, params))
			PanicOnError(orderInner)

			ch <- this.SafeDict(orderInner, 0)
			return nil
		}
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
		}
		var clientOrderId interface{} = this.SafeString2(params, "clOrdId", "clientOrderId")
		if IsTrue(!IsEqual(clientOrderId, nil)) {
			AddElementToObject(request, "clOrdId", clientOrderId)
		} else {
			AddElementToObject(request, "ordId", ToString(id))
		}
		var query interface{} = this.Omit(params, []interface{}{"clOrdId", "clientOrderId"})

		response := (<-this.PrivatePostTradeCancelOrder(this.Extend(request, query)))
		PanicOnError(response)
		// {"code":"0","data":[{"clOrdId":"","ordId":"317251910906576896","sCode":"0","sMsg":""}],"msg":""}
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var order interface{} = this.SafeDict(data, 0)

		ch <- this.ParseOrder(order, market)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseIds(ids interface{}) interface{} {
	/**
	 * @ignore
	 * @method
	 * @name okx#parseIds
	 * @param {string[]|string} ids order ids
	 * @returns {string[]} list of order ids
	 */
	if IsTrue(IsString(ids)) {
		return Split(ids, ",")
	} else {
		return ids
	}
}

/**
 * @method
 * @name okcoin#cancelOrders
 * @description cancel multiple orders
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-multiple-orders
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-algo-order
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-cancel-advance-algo-order
 * @param {string[]} ids order ids
 * @param {string} symbol unified market symbol
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) CancelOrders(ids interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " cancelOrders() requires a symbol argument")))
		}

		retRes17668 := (<-this.LoadMarkets())
		PanicOnError(retRes17668)
		var trigger interface{} = this.SafeValue2(params, "stop", "trigger")
		var advanced interface{} = this.SafeValue(params, "advanced")
		params = this.Omit(params, []interface{}{"stop", "trigger", "advanced"})
		var market interface{} = this.Market(symbol)
		var request interface{} = []interface{}{}
		var clientOrderIds interface{} = this.ParseIds(this.SafeValue2(params, "clOrdId", "clientOrderId"))
		var algoIds interface{} = this.ParseIds(this.SafeValue(params, "algoId"))
		if IsTrue(IsEqual(clientOrderIds, nil)) {
			ids = this.ParseIds(ids)
			if IsTrue(!IsEqual(algoIds, nil)) {
				for i := 0; IsLessThan(i, GetArrayLength(algoIds)); i++ {
					AppendToArray(&request, map[string]interface{}{
						"algoId": GetValue(algoIds, i),
						"instId": GetValue(market, "id"),
					})
				}
			}
			for i := 0; IsLessThan(i, GetArrayLength(ids)); i++ {
				if IsTrue(IsTrue(trigger) || IsTrue(advanced)) {
					AppendToArray(&request, map[string]interface{}{
						"algoId": GetValue(ids, i),
						"instId": GetValue(market, "id"),
					})
				} else {
					AppendToArray(&request, map[string]interface{}{
						"ordId":  GetValue(ids, i),
						"instId": GetValue(market, "id"),
					})
				}
			}
		} else {
			for i := 0; IsLessThan(i, GetArrayLength(clientOrderIds)); i++ {
				AppendToArray(&request, map[string]interface{}{
					"instId":  GetValue(market, "id"),
					"clOrdId": GetValue(clientOrderIds, i),
				})
			}
		}
		var response interface{} = nil
		if IsTrue(trigger) {

			response = (<-this.PrivatePostTradeCancelAlgos(request))
			PanicOnError(response)
		} else if IsTrue(advanced) {

			response = (<-this.PrivatePostTradeCancelAdvanceAlgos(request))
			PanicOnError(response)
		} else {

			response = (<-this.PrivatePostTradeCancelBatchOrders(request))
			PanicOnError(response) // * dont extend with params, otherwise ARRAY will be turned into OBJECT
		}
		//
		//     {
		//         "code": "0",
		//         "data": [
		//             {
		//                 "clOrdId": "e123456789ec4dBC1123456ba123b45e",
		//                 "ordId": "405071912345641543",
		//                 "sCode": "0",
		//                 "sMsg": ""
		//             },
		//             ...
		//         ],
		//         "msg": ""
		//     }
		//
		//
		var ordersData interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(ordersData, market, nil, nil, params)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseOrderStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"canceled":         "canceled",
		"live":             "open",
		"partially_filled": "open",
		"filled":           "closed",
		"effective":        "closed",
	}
	return this.SafeString(statuses, status, status)
}
func (this *okcoin) ParseOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	//
	// createOrder
	//
	//     {
	//         "clOrdId": "oktswap6",
	//         "ordId": "312269865356374016",
	//         "tag": "",
	//         "sCode": "0",
	//         "sMsg": ""
	//     }
	//
	// editOrder
	//
	//     {
	//         "clOrdId": "e847386590ce4dBCc1a045253497a547",
	//         "ordId": "559176536793178112",
	//         "reqId": "",
	//         "sCode": "0",
	//         "sMsg": ""
	//     }
	//
	// Spot and Swap fetchOrder, fetchOpenOrders
	//
	//     {
	//         "accFillSz": "0",
	//         "avgPx": "",
	//         "cTime": "1621910749815",
	//         "category": "normal",
	//         "ccy": "",
	//         "clOrdId": "",
	//         "fee": "0",
	//         "feeCcy": "ETH",
	//         "fillPx": "",
	//         "fillSz": "0",
	//         "fillTime": "",
	//         "instId": "ETH-USDT",
	//         "instType": "SPOT",
	//         "lever": "",
	//         "ordId": "317251910906576896",
	//         "ordType": "limit",
	//         "pnl": "0",
	//         "posSide": "net",
	//         "px": "2000",
	//         "rebate": "0",
	//         "rebateCcy": "USDT",
	//         "side": "buy",
	//         "slOrdPx": "",
	//         "slTriggerPx": "",
	//         "state": "live",
	//         "sz": "0.001",
	//         "tag": "",
	//         "tdMode": "cash",
	//         "tpOrdPx": "",
	//         "tpTriggerPx": "",
	//         "tradeId": "",
	//         "uTime": "1621910749815"
	//     }
	//
	// Algo Order fetchOpenOrders, fetchCanceledOrders, fetchClosedOrders
	//
	//     {
	//         "activePx": "",
	//         "activePxType": "",
	//         "actualPx": "",
	//         "actualSide": "buy",
	//         "actualSz": "0",
	//         "algoId": "431375349042380800",
	//         "cTime": "1649119897778",
	//         "callbackRatio": "",
	//         "callbackSpread": "",
	//         "ccy": "",
	//         "ctVal": "0.01",
	//         "instId": "BTC-USDT-SWAP",
	//         "instType": "SWAP",
	//         "last": "46538.9",
	//         "lever": "125",
	//         "moveTriggerPx": "",
	//         "notionalUsd": "467.059",
	//         "ordId": "",
	//         "ordPx": "50000",
	//         "ordType": "trigger",
	//         "posSide": "long",
	//         "pxLimit": "",
	//         "pxSpread": "",
	//         "pxVar": "",
	//         "side": "buy",
	//         "slOrdPx": "",
	//         "slTriggerPx": "",
	//         "slTriggerPxType": "",
	//         "state": "live",
	//         "sz": "1",
	//         "szLimit": "",
	//         "tag": "",
	//         "tdMode": "isolated",
	//         "tgtCcy": "",
	//         "timeInterval": "",
	//         "tpOrdPx": "",
	//         "tpTriggerPx": "",
	//         "tpTriggerPxType": "",
	//         "triggerPx": "50000",
	//         "triggerPxType": "last",
	//         "triggerTime": "",
	//         "uly": "BTC-USDT"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var id interface{} = this.SafeString2(order, "algoId", "ordId")
	var timestamp interface{} = this.SafeInteger(order, "cTime")
	var lastUpdateTimestamp interface{} = this.SafeInteger(order, "uTime")
	var lastTradeTimestamp interface{} = this.SafeInteger(order, "fillTime")
	var side interface{} = this.SafeString(order, "side")
	var typeVar interface{} = this.SafeString(order, "ordType")
	var postOnly interface{} = nil
	var timeInForce interface{} = nil
	if IsTrue(IsEqual(typeVar, "post_only")) {
		postOnly = true
		typeVar = "limit"
	} else if IsTrue(IsEqual(typeVar, "fok")) {
		timeInForce = "FOK"
		typeVar = "limit"
	} else if IsTrue(IsEqual(typeVar, "ioc")) {
		timeInForce = "IOC"
		typeVar = "limit"
	}
	var marketId interface{} = this.SafeString(order, "instId")
	market = this.SafeMarket(marketId, market)
	var symbol interface{} = this.SafeSymbol(marketId, market, "-")
	var filled interface{} = this.SafeString(order, "accFillSz")
	var price interface{} = this.SafeString2(order, "px", "ordPx")
	var average interface{} = this.SafeString(order, "avgPx")
	var status interface{} = this.ParseOrderStatus(this.SafeString(order, "state"))
	var feeCostString interface{} = this.SafeString(order, "fee")
	var amount interface{} = nil
	var cost interface{} = nil
	// spot market buy: "sz" can refer either to base currency units or to quote currency units
	// see documentation: https://www.okx.com/docs-v5/en/#rest-api-trade-place-order
	var defaultTgtCcy interface{} = this.SafeString(this.Options, "tgtCcy", "base_ccy")
	var tgtCcy interface{} = this.SafeString(order, "tgtCcy", defaultTgtCcy)
	if IsTrue(IsTrue(IsTrue((IsEqual(side, "buy"))) && IsTrue((IsEqual(typeVar, "market")))) && IsTrue((IsEqual(tgtCcy, "quote_ccy")))) {
		// "sz" refers to the cost
		cost = this.SafeString(order, "sz")
	} else {
		// "sz" refers to the trade currency amount
		amount = this.SafeString(order, "sz")
	}
	var fee interface{} = nil
	if IsTrue(!IsEqual(feeCostString, nil)) {
		var feeCostSigned interface{} = Precise.StringNeg(feeCostString)
		var feeCurrencyId interface{} = this.SafeString(order, "feeCcy")
		var feeCurrencyCode interface{} = this.SafeCurrencyCode(feeCurrencyId)
		fee = map[string]interface{}{
			"cost":     this.ParseNumber(feeCostSigned),
			"currency": feeCurrencyCode,
		}
	}
	var clientOrderId interface{} = this.SafeString(order, "clOrdId")
	if IsTrue(IsTrue((!IsEqual(clientOrderId, nil))) && IsTrue((IsLessThan(GetLength(clientOrderId), 1)))) {
		clientOrderId = nil // fix empty clientOrderId string
	}
	var stopLossPrice interface{} = this.SafeNumber2(order, "slTriggerPx", "slOrdPx")
	var takeProfitPrice interface{} = this.SafeNumber2(order, "tpTriggerPx", "tpOrdPx")
	var reduceOnlyRaw interface{} = this.SafeString(order, "reduceOnly")
	var reduceOnly interface{} = false
	if IsTrue(!IsEqual(reduceOnly, nil)) {
		reduceOnly = (IsEqual(reduceOnlyRaw, "true"))
	}
	return this.SafeOrder(map[string]interface{}{
		"info":                order,
		"id":                  id,
		"clientOrderId":       clientOrderId,
		"timestamp":           timestamp,
		"datetime":            this.Iso8601(timestamp),
		"lastTradeTimestamp":  lastTradeTimestamp,
		"lastUpdateTimestamp": lastUpdateTimestamp,
		"symbol":              symbol,
		"type":                typeVar,
		"timeInForce":         timeInForce,
		"postOnly":            postOnly,
		"side":                side,
		"price":               price,
		"stopLossPrice":       stopLossPrice,
		"takeProfitPrice":     takeProfitPrice,
		"triggerPrice":        this.SafeNumberN(order, []interface{}{"triggerPx", "moveTriggerPx"}),
		"average":             average,
		"cost":                cost,
		"amount":              amount,
		"filled":              filled,
		"remaining":           nil,
		"status":              status,
		"fee":                 fee,
		"trades":              nil,
		"reduceOnly":          reduceOnly,
	}, market)
}

/**
 * @method
 * @name okcoin#fetchOrder
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-order-details
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-algo-order-list
 * @description fetches information on an order made by the user
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) FetchOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " fetchOrder() requires a symbol argument")))
		}

		retRes20548 := (<-this.LoadMarkets())
		PanicOnError(retRes20548)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"instId": GetValue(market, "id"),
		}
		var clientOrderId interface{} = this.SafeString2(params, "clOrdId", "clientOrderId")
		var trigger interface{} = this.SafeValue2(params, "stop", "trigger")
		if IsTrue(trigger) {
			if IsTrue(!IsEqual(clientOrderId, nil)) {
				AddElementToObject(request, "algoClOrdId", clientOrderId)
			} else {
				AddElementToObject(request, "algoId", id)
			}
		} else {
			if IsTrue(!IsEqual(clientOrderId, nil)) {
				AddElementToObject(request, "clOrdId", clientOrderId)
			} else {
				AddElementToObject(request, "ordId", id)
			}
		}
		var query interface{} = this.Omit(params, []interface{}{"clientOrderId", "stop", "trigger"})
		var response interface{} = nil
		if IsTrue(trigger) {

			response = (<-this.PrivateGetTradeOrderAlgo(this.Extend(request, query)))
			PanicOnError(response)
		} else {

			response = (<-this.PrivateGetTradeOrder(this.Extend(request, query)))
			PanicOnError(response)
		}
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var order interface{} = this.SafeDict(data, 0)

		ch <- this.ParseOrder(order)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchOpenOrders
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-order-list
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-algo-order-list
 * @description fetch all unfilled currently open orders
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of  open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {bool} [params.trigger] True if fetching trigger or conditional orders
 * @param {string} [params.ordType] "conditional", "oco", "trigger", "move_order_stop", "iceberg", or "twap"
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) FetchOpenOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes21038 := (<-this.LoadMarkets())
		PanicOnError(retRes21038)
		var request interface{} = map[string]interface{}{}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
			AddElementToObject(request, "instId", GetValue(market, "id"))
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit) // default 100, max 100
		}
		var ordType interface{} = this.SafeString(params, "ordType")
		var trigger interface{} = IsTrue(this.SafeValue(params, "stop")) || IsTrue((!IsEqual(this.SafeString(params, "ordType"), nil)))
		if IsTrue(IsTrue(trigger) && IsTrue((IsEqual(ordType, nil)))) {
			AddElementToObject(request, "ordType", "trigger") // default to trigger
		}
		params = this.Omit(params, []interface{}{"stop"})
		var response interface{} = nil
		if IsTrue(trigger) {

			response = (<-this.PrivateGetTradeOrdersAlgoPending(this.Extend(request, params)))
			PanicOnError(response)
		} else {

			response = (<-this.PrivateGetTradeOrdersPending(this.Extend(request, params)))
			PanicOnError(response)
		}
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(data, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchClosedOrders
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-algo-order-history
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-order-history-last-3-months
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-order-history-last-7-days
 * @description fetches information on multiple closed orders made by the user
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {bool} [params.trigger] True if fetching trigger or conditional orders
 * @param {string} [params.ordType] "conditional", "oco", "trigger", "move_order_stop", "iceberg", or "twap"
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *okcoin) FetchClosedOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes21528 := (<-this.LoadMarkets())
		PanicOnError(retRes21528)
		var request interface{} = map[string]interface{}{
			"instType": "SPOT",
		}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
			AddElementToObject(request, "instId", GetValue(market, "id"))
		}
		var ordType interface{} = this.SafeString(params, "ordType")
		var trigger interface{} = IsTrue(this.SafeValue(params, "stop")) || IsTrue((!IsEqual(this.SafeString(params, "ordType"), nil)))
		if IsTrue(IsTrue(trigger) && IsTrue((IsEqual(ordType, nil)))) {
			AddElementToObject(request, "ordType", "trigger") // default to trigger
		}
		params = this.Omit(params, []interface{}{"stop"})
		var response interface{} = nil
		if IsTrue(trigger) {

			response = (<-this.PrivateGetTradeOrdersAlgoHistory(this.Extend(request, params)))
			PanicOnError(response)
		} else {
			var method interface{} = nil
			methodparamsVariable := this.HandleOptionAndParams(params, "fetchClosedOrders", "method", "privateGetTradeOrdersHistory")
			method = GetValue(methodparamsVariable, 0)
			params = GetValue(methodparamsVariable, 1)
			if IsTrue(IsEqual(method, "privateGetTradeOrdersHistory")) {

				response = (<-this.PrivateGetTradeOrdersHistory(this.Extend(request, params)))
				PanicOnError(response)
			} else {

				response = (<-this.PrivateGetTradeOrdersHistoryArchive(this.Extend(request, params)))
				PanicOnError(response)
			}
		}
		//     {
		//         "code": "0",
		//         "data": [
		//             {
		//                 "accFillSz": "0",
		//                 "avgPx": "",
		//                 "cTime": "1621910749815",
		//                 "category": "normal",
		//                 "ccy": "",
		//                 "clOrdId": "",
		//                 "fee": "0",
		//                 "feeCcy": "ETH",
		//                 "fillPx": "",
		//                 "fillSz": "0",
		//                 "fillTime": "",
		//                 "instId": "ETH-USDT",
		//                 "instType": "SPOT",
		//                 "lever": "",
		//                 "ordId": "317251910906576896",
		//                 "ordType": "limit",
		//                 "pnl": "0",
		//                 "posSide": "net",
		//                 "px":"20 00",
		//                 "rebate": "0",
		//                 "rebateCcy": "USDT",
		//                 "side": "buy",
		//                 "slOrdPx": "",
		//                 "slTriggerPx": "",
		//                 "state": "live",
		//                 "sz":"0. 001",
		//                 "tag": "",
		//                 "tdMode": "cash",
		//                 "tpOrdPx": "",
		//                 "tpTriggerPx": "",
		//                 "tradeId": "",
		//                 "uTime": "1621910749815"
		//             }
		//         ],
		//         "msg":""
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(data, market, since, limit)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseDepositAddress(depositAddress interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "addr": "okbtothemoon",
	//         "memo": "971668", // may be missing
	//         "tag":"52055", // may be missing
	//         "pmtId": "", // may be missing
	//         "ccy": "BTC",
	//         "to": "6", // 1 SPOT, 3 FUTURES, 6 FUNDING, 9 SWAP, 12 OPTION, 18 Unified account
	//         "selected": true
	//     }
	//
	//     {
	//         "ccy":"usdt-erc20",
	//         "to":"6",
	//         "addr":"******************************************",
	//         "selected":true
	//     }
	//
	//     {
	//        "chain": "ETH-OKExChain",
	//        "addrEx": { "comment": "6040348" }, // some currencies like TON may have this field,
	//        "ctAddr": "72315c",
	//        "ccy": "ETH",
	//        "to": "6",
	//        "addr": "******************************************",
	//        "selected": true
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var address interface{} = this.SafeString(depositAddress, "addr")
	var tag interface{} = this.SafeStringN(depositAddress, []interface{}{"tag", "pmtId", "memo"})
	if IsTrue(IsEqual(tag, nil)) {
		var addrEx interface{} = this.SafeValue(depositAddress, "addrEx", map[string]interface{}{})
		tag = this.SafeString(addrEx, "comment")
	}
	var currencyId interface{} = this.SafeString(depositAddress, "ccy")
	currency = this.SafeCurrency(currencyId, currency)
	var code interface{} = GetValue(currency, "code")
	var chain interface{} = this.SafeString(depositAddress, "chain")
	var networkId interface{} = Replace(chain, Add(currencyId, "-"), "")
	var network interface{} = this.NetworkIdToCode(networkId)
	// inconsistent naming responses from exchange
	// with respect to network naming provided in currency info vs address chain-names and ids
	//
	// response from address endpoint:
	//      {
	//          "chain": "USDT-Polygon",
	//          "ctAddr": "",
	//          "ccy": "USDT",
	//          "to":"6" ,
	//          "addr": "0x1903441e386cc49d937f6302955b5feb4286dcfa",
	//          "selected": true
	//      }
	// network information from currency['networks'] field:
	// Polygon: {
	//        "info": {
	//            "canDep": false,
	//            "canInternal": false,
	//            "canWd": false,
	//            "ccy": "USDT",
	//            "chain": "USDT-Polygon-Bridge",
	//            "mainNet": false,
	//            "maxFee": "26.879528",
	//            "minFee": "13.439764",
	//            "minWd": "0.001",
	//            "name": ''
	//        },
	//        "id": "USDT-Polygon-Bridge",
	//        "network": "Polygon",
	//        "active": false,
	//        "deposit": false,
	//        "withdraw": false,
	//        "fee": 13.439764,
	//        "precision": undefined,
	//        "limits": {
	//            "withdraw": {
	//                "min": 0.001,
	//                "max": undefined
	//            }
	//        }
	//     },
	//
	this.CheckAddress(address)
	return map[string]interface{}{
		"info":     depositAddress,
		"currency": code,
		"network":  network,
		"address":  address,
		"tag":      tag,
	}
}

/**
 * @method
 * @name okcoin#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://www.okx.com/docs-v5/en/#funding-account-rest-api-get-deposit-address
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *okcoin) FetchDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes23268 := (<-this.LoadMarkets())
		PanicOnError(retRes23268)
		var defaultNetwork interface{} = this.SafeString(this.Options, "defaultNetwork", "ERC20")
		var networkId interface{} = this.SafeString(params, "network", defaultNetwork)
		var networkCode interface{} = this.NetworkIdToCode(networkId)
		params = this.Omit(params, "network")

		response := (<-this.FetchDepositAddressesByNetwork(code, params))
		PanicOnError(response)
		var result interface{} = this.SafeValue(response, networkCode)
		if IsTrue(IsEqual(result, nil)) {
			panic(InvalidAddress(Add(Add(Add(Add(this.Id, " fetchDepositAddress() cannot find "), networkCode), " deposit address for "), code)))
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchDepositAddressesByNetwork
 * @description fetch a dictionary of addresses for a currency, indexed by network
 * @see https://www.okx.com/docs-v5/en/#funding-account-rest-api-get-deposit-address
 * @param {string} code unified currency code of the currency for the deposit address
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [address structures]{@link https://docs.ccxt.com/#/?id=address-structure} indexed by the network
 */
func (this *okcoin) FetchDepositAddressesByNetwork(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes23498 := (<-this.LoadMarkets())
		PanicOnError(retRes23498)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"ccy": GetValue(currency, "id"),
		}

		response := (<-this.PrivateGetAssetDepositAddress(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "addr": "okbtothemoon",
		//                 "memo": "971668", // may be missing
		//                 "tag":"52055", // may be missing
		//                 "pmtId": "", // may be missing
		//                 "ccy": "BTC",
		//                 "to": "6", // 1 SPOT, 3 FUTURES, 6 FUNDING, 9 SWAP, 12 OPTION, 18 Unified account
		//                 "selected": true
		//             },
		//             // {"ccy":"usdt-erc20","to":"6","addr":"******************************************","selected":true},
		//             // {"ccy":"usdt-trc20","to":"6","addr":"TRrd5SiSZrfQVRKm4e9SRSbn2LNTYqCjqx","selected":true},
		//             // {"ccy":"usdt_okexchain","to":"6","addr":"******************************************","selected":true},
		//             // {"ccy":"usdt_kip20","to":"6","addr":"******************************************","selected":true},
		//         ]
		//     }
		//
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var filtered interface{} = this.FilterBy(data, "selected", true)
		var parsed interface{} = this.ParseDepositAddresses(filtered, []interface{}{GetValue(currency, "code")}, false)

		ch <- this.IndexBy(parsed, "network")
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#transfer
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-funding-funds-transfer
 * @description transfer currency internally between wallets on the same account
 * @param {string} code unified currency code
 * @param {float} amount amount to transfer
 * @param {string} fromAccount account to transfer from
 * @param {string} toAccount account to transfer to
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
 */
func (this *okcoin) Transfer(code interface{}, amount interface{}, fromAccount interface{}, toAccount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes23958 := (<-this.LoadMarkets())
		PanicOnError(retRes23958)
		var currency interface{} = this.Currency(code)
		var accountsByType interface{} = this.SafeValue(this.Options, "accountsByType", map[string]interface{}{})
		var fromId interface{} = this.SafeString(accountsByType, fromAccount, fromAccount)
		var toId interface{} = this.SafeString(accountsByType, toAccount, toAccount)
		var request interface{} = map[string]interface{}{
			"ccy":  GetValue(currency, "id"),
			"amt":  this.CurrencyToPrecision(code, amount),
			"type": "0",
			"from": fromId,
			"to":   toId,
		}
		if IsTrue(IsEqual(fromId, "master")) {
			AddElementToObject(request, "type", "1")
			AddElementToObject(request, "subAcct", toId)
			AddElementToObject(request, "from", this.SafeString(params, "from", "6"))
			AddElementToObject(request, "to", this.SafeString(params, "to", "6"))
		} else if IsTrue(IsEqual(toId, "master")) {
			AddElementToObject(request, "type", "2")
			AddElementToObject(request, "subAcct", fromId)
			AddElementToObject(request, "from", this.SafeString(params, "from", "6"))
			AddElementToObject(request, "to", this.SafeString(params, "to", "6"))
		}

		response := (<-this.PrivatePostAssetTransfer(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "transId": "754147",
		//                 "ccy": "USDT",
		//                 "from": "6",
		//                 "amt": "0.1",
		//                 "to": "18"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var rawTransfer interface{} = this.SafeDict(data, 0, map[string]interface{}{})

		ch <- this.ParseTransfer(rawTransfer, currency)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseTransfer(transfer interface{}, optionalArgs ...interface{}) interface{} {
	//
	// transfer
	//
	//     {
	//         "transId": "754147",
	//         "ccy": "USDT",
	//         "from": "6",
	//         "amt": "0.1",
	//         "to": "18"
	//     }
	//
	// fetchTransfer
	//
	//     {
	//         "amt": "5",
	//         "ccy": "USDT",
	//         "from": "18",
	//         "instId": "",
	//         "state": "success",
	//         "subAcct": "",
	//         "to": "6",
	//         "toInstId": "",
	//         "transId": "*********",
	//         "type": "0"
	//     }
	//
	// fetchTransfers
	//
	//     {
	//         "bal": "70.****************",
	//         "balChg": "-4.****************", // negative means "to funding", positive meand "from funding"
	//         "billId": "588900695232225299",
	//         "ccy": "USDT",
	//         "execType": "",
	//         "fee": "",
	//         "from": "18",
	//         "instId": "",
	//         "instType": "",
	//         "mgnMode": "",
	//         "notes": "To Funding Account",
	//         "ordId": "",
	//         "pnl": "",
	//         "posBal": "",
	//         "posBalChg": "",
	//         "price": "0",
	//         "subType": "12",
	//         "sz": "-4",
	//         "to": "6",
	//         "ts": "*************",
	//         "type": "1"
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var id interface{} = this.SafeString2(transfer, "transId", "billId")
	var currencyId interface{} = this.SafeString(transfer, "ccy")
	var code interface{} = this.SafeCurrencyCode(currencyId, currency)
	var amount interface{} = this.SafeNumber(transfer, "amt")
	var fromAccountId interface{} = this.SafeString(transfer, "from")
	var toAccountId interface{} = this.SafeString(transfer, "to")
	var accountsById interface{} = this.SafeValue(this.Options, "accountsById", map[string]interface{}{})
	var timestamp interface{} = this.SafeInteger(transfer, "ts", this.Milliseconds())
	var balanceChange interface{} = this.SafeString(transfer, "sz")
	if IsTrue(!IsEqual(balanceChange, nil)) {
		amount = this.ParseNumber(Precise.StringAbs(balanceChange))
	}
	return map[string]interface{}{
		"info":        transfer,
		"id":          id,
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
		"currency":    code,
		"amount":      amount,
		"fromAccount": this.SafeString(accountsById, fromAccountId),
		"toAccount":   this.SafeString(accountsById, toAccountId),
		"status":      this.ParseTransferStatus(this.SafeString(transfer, "state")),
	}
}
func (this *okcoin) ParseTransferStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"success": "ok",
	}
	return this.SafeString(statuses, status, status)
}

/**
 * @method
 * @name okcoin#withdraw
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-funding-withdrawal
 * @description make a withdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *okcoin) Withdraw(code interface{}, amount interface{}, address interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		tag := GetArg(optionalArgs, 0, nil)
		_ = tag
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		tagparamsVariable := this.HandleWithdrawTagAndParams(tag, params)
		tag = GetValue(tagparamsVariable, 0)
		params = GetValue(tagparamsVariable, 1)
		this.CheckAddress(address)

		retRes25438 := (<-this.LoadMarkets())
		PanicOnError(retRes25438)
		var currency interface{} = this.Currency(code)
		if IsTrue(IsTrue((!IsEqual(tag, nil))) && IsTrue((IsGreaterThan(GetLength(tag), 0)))) {
			address = Add(Add(address, ":"), tag)
		}
		var request interface{} = map[string]interface{}{
			"ccy":    GetValue(currency, "id"),
			"toAddr": address,
			"dest":   "4",
			"amt":    this.NumberToString(amount),
		}
		var network interface{} = this.SafeString(params, "network") // this line allows the user to specify either ERC20 or ETH
		if IsTrue(!IsEqual(network, nil)) {
			var networks interface{} = this.SafeValue(this.Options, "networks", map[string]interface{}{})
			network = this.SafeString(networks, ToUpper(network), network) // handle ETH>ERC20 alias
			AddElementToObject(request, "chain", Add(Add(GetValue(currency, "id"), "-"), network))
			params = this.Omit(params, "network")
		}
		var fee interface{} = this.SafeString(params, "fee")
		if IsTrue(IsEqual(fee, nil)) {
			var targetNetwork interface{} = this.SafeValue(GetValue(currency, "networks"), this.NetworkIdToCode(network), map[string]interface{}{})
			fee = this.SafeString(targetNetwork, "fee")
			if IsTrue(IsEqual(fee, nil)) {
				panic(ArgumentsRequired(Add(this.Id, " withdraw() requires a \"fee\" string parameter, network transaction fee must be ≥ 0. Withdrawals to OKCoin or OKX are fee-free, please set \"0\". Withdrawing to external digital asset address requires network transaction fee.")))
			}
		}
		AddElementToObject(request, "fee", this.NumberToString(fee)) // withdrawals to OKCoin or OKX are fee-free, please set 0

		response := (<-this.PrivatePostAssetWithdrawal(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "amt": "0.1",
		//                 "wdId": "67485",
		//                 "ccy": "BTC"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		var transaction interface{} = this.SafeDict(data, 0)

		ch <- this.ParseTransaction(transaction, currency)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchDeposits
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-funding-get-deposit-history
 * @description fetch all deposits made to an account
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch deposits for
 * @param {int} [limit] the maximum number of deposits structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *okcoin) FetchDeposits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes26018 := (<-this.LoadMarkets())
		PanicOnError(retRes26018)
		var request interface{} = map[string]interface{}{}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "ccy", GetValue(currency, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "before", mathMax(Subtract(since, 1), 0))
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit) // default 100, max 100
		}
		requestparamsVariable := this.HandleUntilOption("after", request, params)
		request = GetValue(requestparamsVariable, 0)
		params = GetValue(requestparamsVariable, 1)

		response := (<-this.PrivateGetAssetDepositHistory(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "amt": "0.01044408",
		//                 "txId": "1915737_3_0_0_asset",
		//                 "ccy": "BTC",
		//                 "from": "13801825426",
		//                 "to": "",
		//                 "ts": "*************",
		//                 "state": "2",
		//                 "depId": "4703879"
		//             },
		//             {
		//                 "amt": "491.6784211",
		//                 "txId": "1744594_3_184_0_asset",
		//                 "ccy": "OKB",
		//                 "from": "",
		//                 "to": "",
		//                 "ts": "*************",
		//                 "state": "2",
		//                 "depId": "4703809"
		//             },
		//             {
		//                 "amt": "223.18782496",
		//                 "txId": "6d892c669225b1092c780bf0da0c6f912fc7dc8f6b8cc53b003288624c",
		//                 "ccy": "USDT",
		//                 "from": "",
		//                 "to": "**********************************",
		//                 "ts": "*************",
		//                 "state": "2",
		//                 "depId": "4703779"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTransactions(data, currency, since, limit, params)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchWithdrawals
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-funding-get-withdrawal-history
 * @description fetch all withdrawals made from an account
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch withdrawals for
 * @param {int} [limit] the maximum number of withdrawals structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *okcoin) FetchWithdrawals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes26768 := (<-this.LoadMarkets())
		PanicOnError(retRes26768)
		var request interface{} = map[string]interface{}{}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "ccy", GetValue(currency, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "before", mathMax(Subtract(since, 1), 0))
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit) // default 100, max 100
		}
		requestparamsVariable := this.HandleUntilOption("after", request, params)
		request = GetValue(requestparamsVariable, 0)
		params = GetValue(requestparamsVariable, 1)

		response := (<-this.PrivateGetAssetWithdrawalHistory(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "amt": "0.094",
		//                 "wdId": "4703879",
		//                 "fee": "0.01000000eth",
		//                 "txId": "0x62477bac6509a04512819bb1455e923a60dea5966c7caeaa0b24eb8fb0432b85",
		//                 "ccy": "ETH",
		//                 "from": "***********",
		//                 "to": "******************************************",
		//                 "ts": "*************",
		//                 "state": "2"
		//             },
		//             {
		//                 "amt": "0.01",
		//                 "wdId": "4703879",
		//                 "fee": "0.00000000btc",
		//                 "txId": "",
		//                 "ccy": "BTC",
		//                 "from": "***********",
		//                 "to": "***********",
		//                 "ts": "*************",
		//                 "state": "2"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTransactions(data, currency, since, limit, params)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseTransactionStatus(status interface{}) interface{} {
	//
	// deposit statuses
	//
	//     {
	//         "0": "waiting for confirmation",
	//         "1": "confirmation account",
	//         "2": "recharge success"
	//     }
	//
	// withdrawal statues
	//
	//     {
	//        '-3': "pending cancel",
	//        "-2": "cancelled",
	//        "-1": "failed",
	//         "0": "pending",
	//         "1": "sending",
	//         "2": "sent",
	//         "3": "email confirmation",
	//         "4": "manual confirmation",
	//         "5": "awaiting identity confirmation"
	//     }
	//
	var statuses interface{} = map[string]interface{}{
		"-3": "pending",
		"-2": "canceled",
		"-1": "failed",
		"0":  "pending",
		"1":  "pending",
		"2":  "ok",
		"3":  "pending",
		"4":  "pending",
		"5":  "pending",
	}
	return this.SafeString(statuses, status, status)
}
func (this *okcoin) ParseTransaction(transaction interface{}, optionalArgs ...interface{}) interface{} {
	//
	// withdraw
	//
	//     {
	//         "amt": "0.1",
	//         "wdId": "67485",
	//         "ccy": "BTC"
	//     }
	//
	// fetchWithdrawals
	//
	//     {
	//         "amt": "0.094",
	//         "wdId": "4703879",
	//         "fee": "0.01000000eth",
	//         "txId": "0x62477bac6509a04512819bb1455e923a60dea5966c7caeaa0b24eb8fb0432b85",
	//         "ccy": "ETH",
	//         "from": "***********",
	//         "to": "******************************************",
	//         "tag",
	//         "pmtId",
	//         "memo",
	//         "ts": "*************",
	//         "state": "2"
	//     }
	//
	// fetchDeposits
	//
	//     {
	//         "amt": "0.01044408",
	//         "txId": "1915737_3_0_0_asset",
	//         "ccy": "BTC",
	//         "from": "13801825426",
	//         "to": "",
	//         "ts": "*************",
	//         "state": "2",
	//         "depId": "4703879"
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var typeVar interface{} = nil
	var id interface{} = nil
	var withdrawalId interface{} = this.SafeString(transaction, "wdId")
	var addressFrom interface{} = this.SafeString(transaction, "from")
	var addressTo interface{} = this.SafeString(transaction, "to")
	var address interface{} = addressTo
	var tagTo interface{} = this.SafeString2(transaction, "tag", "memo")
	tagTo = this.SafeString2(transaction, "pmtId", tagTo)
	if IsTrue(!IsEqual(withdrawalId, nil)) {
		typeVar = "withdrawal"
		id = withdrawalId
	} else {
		// the payment_id will appear on new deposits but appears to be removed from the response after 2 months
		id = this.SafeString(transaction, "depId")
		typeVar = "deposit"
	}
	var currencyId interface{} = this.SafeString(transaction, "ccy")
	var code interface{} = this.SafeCurrencyCode(currencyId)
	var amount interface{} = this.SafeNumber(transaction, "amt")
	var status interface{} = this.ParseTransactionStatus(this.SafeString(transaction, "state"))
	var txid interface{} = this.SafeString(transaction, "txId")
	var timestamp interface{} = this.SafeInteger(transaction, "ts")
	var feeCost interface{} = nil
	if IsTrue(IsEqual(typeVar, "deposit")) {
		feeCost = 0
	} else {
		feeCost = this.SafeNumber(transaction, "fee")
	}
	// todo parse tags
	return map[string]interface{}{
		"info":        transaction,
		"id":          id,
		"currency":    code,
		"amount":      amount,
		"network":     nil,
		"addressFrom": addressFrom,
		"addressTo":   addressTo,
		"address":     address,
		"tagFrom":     nil,
		"tagTo":       tagTo,
		"tag":         tagTo,
		"status":      status,
		"type":        typeVar,
		"updated":     nil,
		"txid":        txid,
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
		"comment":     nil,
		"internal":    nil,
		"fee": map[string]interface{}{
			"currency": code,
			"cost":     feeCost,
		},
	}
}

/**
 * @method
 * @name okcoin#fetchMyTrades
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-transaction-details-last-3-days
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-trade-get-transaction-details-last-3-months
 * @description fetch all trades made by the user
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *okcoin) FetchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes28788 := (<-this.LoadMarkets())
		PanicOnError(retRes28788)
		var request interface{} = map[string]interface{}{
			"instType": "SPOT",
		}
		if IsTrue(IsTrue((!IsEqual(limit, nil))) && IsTrue((IsGreaterThan(limit, 100)))) {
			limit = 100
		}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
			AddElementToObject(request, "instId", GetValue(market, "id"))
		}
		var method interface{} = nil
		methodparamsVariable := this.HandleOptionAndParams(params, "fetchMyTrades", "method", "privateGetTradeFillsHistory")
		method = GetValue(methodparamsVariable, 0)
		params = GetValue(methodparamsVariable, 1)
		var response interface{} = nil
		if IsTrue(IsEqual(method, "privateGetTradeFillsHistory")) {

			response = (<-this.PrivateGetTradeFillsHistory(this.Extend(request, params)))
			PanicOnError(response)
		} else {

			response = (<-this.PrivateGetTradeFills(this.Extend(request, params)))
			PanicOnError(response)
		}
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTrades(data, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchOrderTrades
 * @description fetch all the trades made from a single order
 * @param {string} id order id
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *okcoin) FetchOrderTrades(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{
			"order_id": id,
		}

		retRes292115 := (<-this.FetchMyTrades(symbol, since, limit, this.Extend(request, params)))
		PanicOnError(retRes292115)
		ch <- retRes292115
		return nil

	}()
	return ch
}

/**
 * @method
 * @name okcoin#fetchLedger
 * @description fetch the history of changes, actions done by the user or operations that altered the balance of the user
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-funding-asset-bills-details
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-account-get-bills-details-last-7-days
 * @see https://www.okcoin.com/docs-v5/en/#rest-api-account-get-bills-details-last-3-months
 * @param {string} [code] unified currency code, default is undefined
 * @param {int} [since] timestamp in ms of the earliest ledger entry, default is undefined
 * @param {int} [limit] max number of ledger entries to return, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ledger structure]{@link https://docs.ccxt.com/#/?id=ledger}
 */
func (this *okcoin) FetchLedger(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes29388 := (<-this.LoadMarkets())
		PanicOnError(retRes29388)
		var method interface{} = nil
		methodparamsVariable := this.HandleOptionAndParams(params, "fetchLedger", "method", "privateGetAccountBills")
		method = GetValue(methodparamsVariable, 0)
		params = GetValue(methodparamsVariable, 1)
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "ccy", GetValue(currency, "id"))
		}
		requestparamsVariable := this.HandleUntilOption("end", request, params)
		request = GetValue(requestparamsVariable, 0)
		params = GetValue(requestparamsVariable, 1)
		var response interface{} = nil
		if IsTrue(IsEqual(method, "privateGetAccountBillsArchive")) {

			response = (<-this.PrivateGetAccountBillsArchive(this.Extend(request, params)))
			PanicOnError(response)
		} else if IsTrue(IsEqual(method, "privateGetAssetBills")) {

			response = (<-this.PrivateGetAssetBills(this.Extend(request, params)))
			PanicOnError(response)
		} else {

			response = (<-this.PrivateGetAccountBills(this.Extend(request, params)))
			PanicOnError(response)
		}
		//
		// privateGetAccountBills, privateGetAccountBillsArchive
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "bal": "0.****************",
		//                 "balChg": "-664.****************",
		//                 "billId": "310394313544966151",
		//                 "ccy": "USDT",
		//                 "fee": "0",
		//                 "from": "",
		//                 "instId": "LTC-USDT",
		//                 "instType": "SPOT",
		//                 "mgnMode": "cross",
		//                 "notes": "",
		//                 "ordId": "310394313519800320",
		//                 "pnl": "0",
		//                 "posBal": "0",
		//                 "posBalChg": "0",
		//                 "subType": "2",
		//                 "sz": "664.********",
		//                 "to": "",
		//                 "ts": "*************",
		//                 "type": "2"
		//             }
		//         ]
		//     }
		//
		// privateGetAssetBills
		//
		//     {
		//         "code": "0",
		//         "msg": "",
		//         "data": [
		//             {
		//                 "billId": "12344",
		//                 "ccy": "BTC",
		//                 "balChg": "2",
		//                 "bal": "12",
		//                 "type": "1",
		//                 "ts": "*************"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeValue(response, "data", []interface{}{})

		ch <- this.ParseLedger(data, currency, since, limit)
		return nil

	}()
	return ch
}
func (this *okcoin) ParseLedgerEntryType(typeVar interface{}) interface{} {
	var types interface{} = map[string]interface{}{
		"1":  "transfer",
		"2":  "trade",
		"3":  "trade",
		"4":  "rebate",
		"5":  "trade",
		"6":  "transfer",
		"7":  "trade",
		"8":  "fee",
		"9":  "trade",
		"10": "trade",
		"11": "trade",
	}
	return this.SafeString(types, typeVar, typeVar)
}
func (this *okcoin) ParseLedgerEntry(item interface{}, optionalArgs ...interface{}) interface{} {
	//
	// privateGetAccountBills, privateGetAccountBillsArchive
	//
	//     {
	//         "bal": "0.****************",
	//         "balChg": "-664.****************",
	//         "billId": "310394313544966151",
	//         "ccy": "USDT",
	//         "fee": "0",
	//         "from": "",
	//         "instId": "LTC-USDT",
	//         "instType": "SPOT",
	//         "mgnMode": "cross",
	//         "notes": "",
	//         "ordId": "310394313519800320",
	//         "pnl": "0",
	//         "posBal": "0",
	//         "posBalChg": "0",
	//         "subType": "2",
	//         "sz": "664.********",
	//         "to": "",
	//         "ts": "*************",
	//         "type": "2"
	//     }
	//
	// privateGetAssetBills
	//
	//     {
	//         "billId": "12344",
	//         "ccy": "BTC",
	//         "balChg": "2",
	//         "bal": "12",
	//         "type": "1",
	//         "ts": "*************"
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var currencyId interface{} = this.SafeString(item, "ccy")
	var code interface{} = this.SafeCurrencyCode(currencyId, currency)
	currency = this.SafeCurrency(currencyId, currency)
	var timestamp interface{} = this.SafeInteger(item, "ts")
	var feeCostString interface{} = this.SafeString(item, "fee")
	var fee interface{} = nil
	if IsTrue(!IsEqual(feeCostString, nil)) {
		fee = map[string]interface{}{
			"cost":     this.ParseToNumeric(Precise.StringNeg(feeCostString)),
			"currency": code,
		}
	}
	var marketId interface{} = this.SafeString(item, "instId")
	var symbol interface{} = this.SafeSymbol(marketId, nil, "-")
	return this.SafeLedgerEntry(map[string]interface{}{
		"info":             item,
		"id":               this.SafeString(item, "billId"),
		"timestamp":        timestamp,
		"datetime":         this.Iso8601(timestamp),
		"account":          nil,
		"referenceId":      this.SafeString(item, "ordId"),
		"referenceAccount": nil,
		"type":             this.ParseLedgerEntryType(this.SafeString(item, "type")),
		"currency":         code,
		"symbol":           symbol,
		"amount":           this.SafeNumber(item, "balChg"),
		"before":           nil,
		"after":            this.SafeNumber(item, "bal"),
		"status":           "ok",
		"fee":              fee,
	}, currency)
}
func (this *okcoin) Sign(path interface{}, optionalArgs ...interface{}) interface{} {
	api := GetArg(optionalArgs, 0, "public")
	_ = api
	method := GetArg(optionalArgs, 1, "GET")
	_ = method
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	headers := GetArg(optionalArgs, 3, nil)
	_ = headers
	body := GetArg(optionalArgs, 4, nil)
	_ = body
	var isArray interface{} = IsArray(params)
	var request interface{} = Add(Add(Add("/api/", this.Version), "/"), this.ImplodeParams(path, params))
	var query interface{} = this.Omit(params, this.ExtractParams(path))
	var url interface{} = Add(this.ImplodeHostname(GetValue(GetValue(this.Urls, "api"), "rest")), request)
	if IsTrue(IsEqual(api, "public")) {
		if IsTrue(GetArrayLength(ObjectKeys(query))) {
			url = Add(url, Add("?", this.Urlencode(query)))
		}
	} else if IsTrue(IsEqual(api, "private")) {
		this.CheckRequiredCredentials()
		var timestamp interface{} = this.Iso8601(this.Milliseconds())
		headers = map[string]interface{}{
			"OK-ACCESS-KEY":        this.ApiKey,
			"OK-ACCESS-PASSPHRASE": this.Password,
			"OK-ACCESS-TIMESTAMP":  timestamp,
		}
		var auth interface{} = Add(Add(timestamp, method), request)
		if IsTrue(IsEqual(method, "GET")) {
			if IsTrue(GetArrayLength(ObjectKeys(query))) {
				var urlencodedQuery interface{} = Add("?", this.Urlencode(query))
				url = Add(url, urlencodedQuery)
				auth = Add(auth, urlencodedQuery)
			}
		} else {
			if IsTrue(IsTrue(isArray) || IsTrue(GetArrayLength(ObjectKeys(query)))) {
				body = this.Json(query)
				auth = Add(auth, body)
			}
			AddElementToObject(headers, "Content-Type", "application/json")
		}
		var signature interface{} = this.Hmac(this.Encode(auth), this.Encode(this.Secret), sha256, "base64")
		AddElementToObject(headers, "OK-ACCESS-SIGN", signature)
	}
	return map[string]interface{}{
		"url":     url,
		"method":  method,
		"body":    body,
		"headers": headers,
	}
}
func (this *okcoin) ParseBalanceByType(typeVar interface{}, response interface{}) interface{} {
	if IsTrue(IsEqual(typeVar, "funding")) {
		return this.ParseFundingBalance(response)
	} else {
		return this.ParseTradingBalance(response)
	}
}
func (this *okcoin) HandleErrors(httpCode interface{}, reason interface{}, url interface{}, method interface{}, headers interface{}, body interface{}, response interface{}, requestHeaders interface{}, requestBody interface{}) interface{} {
	if !IsTrue(response) {
		return nil // fallback to default error handler
	}
	//
	//    {
	//        "code": "1",
	//        "data": [
	//            {
	//                "clOrdId": "",
	//                "ordId": "",
	//                "sCode": "51119",
	//                "sMsg": "Order placement failed due to insufficient balance. ",
	//                "tag": ""
	//            }
	//        ],
	//        "msg": ""
	//    },
	//    {
	//        "code": "58001",
	//        "data": [],
	//        "msg": "Incorrect trade password"
	//    }
	//
	var code interface{} = this.SafeString(response, "code")
	if IsTrue(!IsEqual(code, "0")) {
		var feedback interface{} = Add(Add(this.Id, " "), body)
		var data interface{} = this.SafeValue(response, "data", []interface{}{})
		for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
			var error interface{} = GetValue(data, i)
			var errorCode interface{} = this.SafeString(error, "sCode")
			var message interface{} = this.SafeString(error, "sMsg")
			this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), errorCode, feedback)
			this.ThrowBroadlyMatchedException(GetValue(this.Exceptions, "broad"), message, feedback)
		}
		this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), code, feedback)
		panic(ExchangeError(feedback))
	}
	return nil
}

func (this *okcoin) Init(userConfig map[string]interface{}) {
	this.Exchange = Exchange{}
	this.Exchange.DerivedExchange = this
	this.Exchange.InitParent(userConfig, this.Describe().(map[string]interface{}), this)
}
