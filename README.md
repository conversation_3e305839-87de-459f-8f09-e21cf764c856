# crypto-monitor
## Different exchanges nuances:
- Binance
    - can use both watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols.
    - uses multiple clients (ws-connections) for watchOrderBook and watchOrderBookForSymbols (sometimes mixed and dynamically merged).
    - we use 'exchange-connections-helpers.ts' to close the connections when we are done watching them.
- Bybit
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we use 'monitored-symbols-registry.ts' keep track of monitored symbols and force call "unWatchOrderBook" when we are done watching them.
    - it also doesn't allow to start watching more than 10 symbols in less than 500ms timeframe.
    - we use 'exchange-watch-sheduler.ts' to split the symbols into slots of 10 (or less) and add delays between starting the groups watching.
    - max group size is 10 symbols as well.
- Coinbase
    - uses single client for watchOrderBook and watchOrderBookForSymbols.
    - does not support unwatching symbols... at all.
    - only supports 30 symbols per client/connection.
    - we use fetchOrderBook instead of watch<PERSON>rderBook for it.
    - we use multiple group monitors with a separate exchange instance for each monitor.
    - when we finish monitoring all symbols, we close the exchange instance of the group monitor.
- okx
  -   correctMonitoredRequests: 250/250, inFirstUpdateState: 4, uniqueMonitoredSymbols: 96
      clients:1
      symbols:
      1/1 individualMonitors '1':(30)
      fetchWatcher (36): INJ/USDT, IMX/USDT, XLM/USDT, ETC/USDT, ICP/USDT, UNI/USDT, LTC/USDC, LTC/USDT, NEAR/USDT, BCH/USDT, LINK/USDT, TRX/USDC, TRX/USDT, DOT/USDT, SHIB/USDT, AVAX/USDT, ADA/USDC, ADA/USDT, TON/USDC, TON/USDT, DOGE/USDC, DOGE/USDT, XRP/USDC, XRP/USDT, SOL/USDC, SOL/USDT, BNB/USDT, ETH/USDC, ETH/USDT, BTC/USDC, BTC/USDT, AAVE/USDT, SUI/USDT, FIL/USDT, ONDO/USDT, ATOM/USDT
      5/5 groupMonitors '1':(3) '2':(4) '3':(1) '6':(1) '7':(21)
  -  ERROR: WatchGroupOrderbookMonitor '2' 'watchSymbols' error:
     err: {
     "type": "NetworkError",
     "message": "WebSocket was closed before the connection was established",
     "stack":
     NetworkError: WebSocket was closed before the connection was established
     at WsClient.onError (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:208:21)
     at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
     at WebSocket.onError (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:230:9)
     at WebSocket.emit (node:events:507:28)
     at emitErrorAndClose (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1041:13)
     at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
     "name": "NetworkError"
     }
- bitget
  - correctMonitoredRequests: 350/350, inFirstUpdateState: 0, uniqueMonitoredSymbols: 107
    clients:1
    symbols:
    1/1 individualMonitors '1':(26)
    fetchWatcher (2): LINK/USDT, TRX/USDC
    6/6 groupMonitors '1':(4) '2':(6) '3':(5) '4':(26) '5':(25) '6':(13)
  - ERROR: WatchGroupOrderbookMonitor '5' 'unwatchSymbol' 'TIA/USDT' error:
    err: {
    "type": "RateLimitExceeded",
    "message": "bitget {\"event\":\"error\",\"code\":30006,\"msg\":\"request too many\"}",
    "stack":
    RateLimitExceeded: bitget {"event":"error","code":30006,"msg":"request too many"}
    at bitget.throwExactlyMatchedException (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/Exchange.js:4936:19)
    at bitget.handleErrorMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2125:22)
    at bitget.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2228:18)
    at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
    at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Receiver.dataMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/receiver.js:596:14)
    "name": "RateLimitExceeded"
    }
  - ERROR: WatchIndividualExchangeMonitor 'watchSymbol' COMP/USDT error:
    err: {
    "type": "UnsubscribeError",
    "message": "bitget orderbook COMP/USDT",
    "stack":
    UnsubscribeError: bitget orderbook COMP/USDT
    at bitget.handleOrderBookUnSubscription (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2318:23)
    at bitget.handleUnSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2444:22)
    at bitget.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/bitget.js:2250:18)
    at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
    at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
    at Receiver.receiverOnMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1220:20)
    at Receiver.emit (node:events:507:28)
    at Receiver.dataMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/receiver.js:596:14)
    "name": "UnsubscribeError"
    }


- Mexc
    - only supports watchOrderBook/unWatchOrderBook methods.
    - watchOrderBook is buggy - not working with several instances of individual monitors sharing the same exchange instance (method never finishes when called many times at once).
    - uses single client/connection.
    - cannot handle more than 70 symbols per connection.
    - we use individual monitors (several) for it with separate exchange instances.
    - each individual monitor can monitor up to 30 symbols (limited on purpose to improve stability).
    - when we finish monitoring all symbols, we close the exchange instance of the individual monitor.
- Gate
    - only supports watchOrderBook method.
    - does not support unwatching symbols, however it does have 'unWatchOrderBook' implementation.
    - we let start watching 1 symbol per second and use force unwatch for it.
- HTX
    - only supports watchOrderBook method.
    - does not support unwatching symbols... at all.
    - when we monitor a lot of symbols on a single instance of individual monitor, it leads to 'invalid nounce' error.
    - we've found that it works stable with 3 symbols per individual monitor with a separate exchange instance for each monitor.
    (node:2967) TimeoutNegativeWarning: -2 is a negative number.
Timeout duration was set to 1.
    at Timeout (node:internal/timers:195:17)
    at setTimeout (node:timers:163:19)
    at htx.delay (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/Exchange.js:920:9)
    at htx.handleOrderBookSnapshot (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:427:30)
    at htx.handleSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:1717:24)
    at htx.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:2049:22)
    at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
    at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
    at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
    at WebSocket.emit (node:events:507:28)
[06:57:17.362] ERROR: WatchIndividualExchangeMonitor 1 'watchSymbol' STRK/USDT error: 
    err: {
      "type": "InvalidNonce",
      "message": "htx failed to synchronize WebSocket feed with the snapshot for symbol STRK/USDT in 3 attempts",
      "stack":
          InvalidNonce: htx failed to synchronize WebSocket feed with the snapshot for symbol STRK/USDT in 3 attempts
              at htx.handleOrderBookSnapshot (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:432:27)
              at htx.handleSubscriptionStatus (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:1717:24)
              at htx.handleMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/pro/htx.js:2049:22)
              at WsClient.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ccxt/dist/cjs/src/base/ws/Client.js:305:18)
              at callListener (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:290:14)
              at WebSocket.onMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/event-target.js:209:9)
              at WebSocket.emit (node:events:507:28)
              at Receiver.receiverOnMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/websocket.js:1220:20)
              at Receiver.emit (node:events:507:28)
              at Receiver.dataMessage (/Users/<USER>/Developer/Crypto/crypto-monitor/node_modules/ws/lib/receiver.js:569:14)
      "name": "InvalidNonce"
    }
</logs>

    - Kucoin
    - supports both watchOrderBook and watchOrderBookForSymbols.
    - only supports unwatchOrderbook.
    - throws errors if there are too many resubscribtions happening at once.



// to do:
- probably there is some issue with finishing watching symbols if monitor is in stage of awaiting the start spread delay and at this time we clear its subscribers (upgrade).
- i saw that a dynamic group monitor was created for 10 symbol, but then no 'upgrade' logs followed and in the end this group didn't close when all requests removed. next test with resubscription and unsubscription seemed to work fine though.
- okx,bitget UnsubscribeError are still quite often. it seems that after forceUnwatch, symbols are harder to restore their connection. done.
- we should avoid using fetch orderbooks for coinbase.
- need to write a special rebalance for static individual monitors, such as coinbase.
it should recreate individual exchanges, when current serve mostly not needed symbols. 
- sometimes i see that individual monitor has symbols long time after all subscribers are removed. that's probably because of low trading activity on exchange. the orderbook update happens very rarely, so watchOrderbook cannot finish and thus we cannot unwatch it.